#!/bin/bash

# ConfidentCoaching Production Deployment Script
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if required files exist
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
    fi
    
    # Check if environment file exists
    if [ ! -f ".env.production" ] && [ ! -f ".env" ]; then
        error "Environment file not found. Please create .env.production or .env file."
    fi
    
    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "./logs"
    mkdir -p "./uploads"
    mkdir -p "./ssl"
    
    success "Directories created"
}

# Backup database
backup_database() {
    log "Creating database backup..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps db | grep -q "Up"; then
        BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
        
        docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T db pg_dump -U postgres confidentcoaching > "$BACKUP_FILE"
        
        if [ $? -eq 0 ]; then
            success "Database backup created: $BACKUP_FILE"
        else
            warning "Database backup failed, but continuing deployment"
        fi
    else
        warning "Database container not running, skipping backup"
    fi
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    success "Images pulled successfully"
}

# Deploy application
deploy() {
    log "Deploying application..."
    
    # Stop existing containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start new containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Application deployed"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations
    docker-compose -f "$DOCKER_COMPOSE_FILE" exec app npm run migrate
    
    if [ $? -eq 0 ]; then
        success "Database migrations completed"
    else
        error "Database migrations failed"
    fi
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Wait for application to start
    sleep 15
    
    # Check if application is responding
    for i in {1..30}; do
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            success "Application is healthy"
            return 0
        fi
        
        log "Waiting for application to start... (attempt $i/30)"
        sleep 2
    done
    
    error "Health check failed - application is not responding"
}

# Cleanup old images
cleanup() {
    log "Cleaning up old Docker images..."
    
    docker system prune -f
    
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting ConfidentCoaching deployment..."
    
    check_prerequisites
    create_directories
    backup_database
    pull_images
    deploy
    run_migrations
    health_check
    cleanup
    
    success "Deployment completed successfully!"
    log "Application is now running at http://localhost:3001"
    log "Health check endpoint: http://localhost:3001/health"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        backup_database
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "logs")
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f
        ;;
    "stop")
        log "Stopping application..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        success "Application stopped"
        ;;
    "restart")
        log "Restarting application..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" restart
        health_check
        success "Application restarted"
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|cleanup|logs|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment (default)"
        echo "  backup   - Create database backup"
        echo "  health   - Check application health"
        echo "  cleanup  - Clean up old Docker images"
        echo "  logs     - Show application logs"
        echo "  stop     - Stop the application"
        echo "  restart  - Restart the application"
        exit 1
        ;;
esac
