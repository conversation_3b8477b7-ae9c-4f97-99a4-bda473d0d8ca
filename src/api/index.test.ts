import request from 'supertest';
import { createApiServer } from './index';

// Mock the routes module
jest.mock('./routes', () => {
  const express = require('express');
  const router = express.Router();
  
  router.get('/test', (req: any, res: any) => {
    res.json({ message: 'test endpoint' });
  });
  
  router.get('/error', (req: any, res: any, next: any) => {
    const error = new Error('Test error');
    next(error);
  });
  
  return router;
});

describe('API Server', () => {
  let app: any;

  beforeEach(() => {
    app = createApiServer();
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        environment: 'test'
      });
      expect(response.body.timestamp).toBeDefined();
      expect(response.body.uptime).toBeDefined();
    });
  });

  describe('API Routes', () => {
    it('should handle valid API routes', async () => {
      const response = await request(app)
        .get('/api/test')
        .expect(200);

      expect(response.body).toEqual({
        message: 'test endpoint'
      });
    });

    it('should return 404 for non-existent API routes', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body).toMatchObject({
        error: 'API endpoint not found',
        path: '/nonexistent'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully', async () => {
      const response = await request(app)
        .get('/api/error')
        .expect(500);

      expect(response.body).toMatchObject({
        error: 'Test error'
      });
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Check for some security headers set by helmet
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
    });
  });

  describe('CORS', () => {
    it('should handle CORS preflight requests', async () => {
      await request(app)
        .options('/api/test')
        .expect(204);
    });

    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/api/test')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to API routes', async () => {
      // Make multiple requests to test rate limiting
      const requests = Array(10).fill(null).map(() => 
        request(app).get('/api/test')
      );

      const responses = await Promise.all(requests);
      
      // All requests should succeed in test environment
      // (rate limiting is usually more lenient in tests)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });
});
