-- Enhanced Line Manager Schema for Network-Based Employee Development
-- Inspired by Team Topologies and From Bud to Boss principles

-- ============================================================================
-- CORE ENTITIES
-- ============================================================================

-- Enhanced Users table with team topology awareness
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  role VARCHAR(50) NOT NULL DEFAULT 'employee',
  employee_id VARCHAR(50) UNIQUE, -- External employee ID
  department VARCHAR(100),
  job_title VARCHAR(150),
  hire_date DATE,
  manager_id INTEGER REFERENCES users(id),
  team_type VARCHAR(50) DEFAULT 'stream-aligned', -- Team Topologies: stream-aligned, platform, enabling, complicated-subsystem
  cognitive_load_level INTEGER DEFAULT 5 CHECK (cognitive_load_level >= 1 AND cognitive_load_level <= 10),
  preferred_communication_style VARCHAR(50) DEFAULT 'collaborative', -- directive, collaborative, supportive, delegating
  career_stage VARCHAR(50) DEFAULT 'developing', -- new, developing, proficient, expert, master
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Skills and Competencies Framework
CREATE TABLE IF NOT EXISTS skills (
  id SERIAL PRIMARY KEY,
  name VARCHAR(150) NOT NULL UNIQUE,
  category VARCHAR(100) NOT NULL, -- technical, leadership, communication, domain-specific
  skill_type VARCHAR(50) NOT NULL, -- hard, soft, hybrid
  description TEXT,
  proficiency_levels JSONB DEFAULT '["novice", "advanced-beginner", "competent", "proficient", "expert"]',
  is_core_skill BOOLEAN DEFAULT false, -- Core skills for the organization
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee Skills with proficiency tracking
CREATE TABLE IF NOT EXISTS employee_skills (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  skill_id INTEGER NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
  current_level VARCHAR(50) NOT NULL DEFAULT 'novice',
  target_level VARCHAR(50),
  last_assessed_date DATE,
  assessed_by INTEGER REFERENCES users(id),
  evidence_notes TEXT,
  development_priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(employee_id, skill_id)
);

-- Career Aspirations and Development Paths
CREATE TABLE IF NOT EXISTS career_aspirations (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  aspiration_type VARCHAR(50) NOT NULL, -- role, skill, responsibility, location, team
  target_role VARCHAR(150),
  target_department VARCHAR(100),
  target_timeline VARCHAR(50), -- 6-months, 1-year, 2-years, 5-years
  motivation TEXT,
  current_gap_analysis TEXT,
  development_actions TEXT,
  probability_score INTEGER CHECK (probability_score >= 1 AND probability_score <= 10),
  status VARCHAR(50) DEFAULT 'active', -- active, achieved, modified, abandoned
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- INTERACTION AND RELATIONSHIP TRACKING
-- ============================================================================

-- Line Manager Interactions (1:1s, coaching sessions, feedback)
CREATE TABLE IF NOT EXISTS manager_interactions (
  id SERIAL PRIMARY KEY,
  manager_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  interaction_type VARCHAR(50) NOT NULL, -- one-on-one, coaching, feedback, check-in, career-discussion
  interaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER,
  location VARCHAR(100), -- office, remote, walking, coffee
  
  -- Structured interaction data
  topics_discussed TEXT[],
  employee_mood VARCHAR(50), -- energized, motivated, concerned, frustrated, confused
  engagement_level INTEGER CHECK (engagement_level >= 1 AND engagement_level <= 10),
  
  -- Key conversation elements
  wins_celebrated TEXT,
  challenges_discussed TEXT,
  support_requested TEXT,
  actions_agreed JSONB, -- [{"action": "...", "owner": "...", "due_date": "..."}]
  
  -- Manager observations
  manager_notes TEXT,
  development_opportunities_identified TEXT,
  follow_up_required BOOLEAN DEFAULT false,
  follow_up_date DATE,
  
  -- Relationship building
  personal_updates TEXT, -- Family, interests, life events
  trust_building_notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Peer Relationships and Collaboration Networks
CREATE TABLE IF NOT EXISTS peer_relationships (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  peer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  relationship_type VARCHAR(50) NOT NULL, -- mentor, mentee, collaborator, knowledge-sharer, project-partner
  relationship_strength VARCHAR(20) DEFAULT 'weak', -- weak, moderate, strong
  collaboration_frequency VARCHAR(30) DEFAULT 'occasional', -- daily, weekly, monthly, quarterly, occasional
  knowledge_flow_direction VARCHAR(20) DEFAULT 'bidirectional', -- to-peer, from-peer, bidirectional
  
  -- Relationship context
  context TEXT, -- How they work together
  shared_projects INTEGER[], -- Array of project IDs
  shared_skills INTEGER[], -- Array of skill IDs
  
  -- Relationship health
  last_interaction_date DATE,
  relationship_quality INTEGER CHECK (relationship_quality >= 1 AND relationship_quality <= 10),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(employee_id, peer_id)
);

-- ============================================================================
-- ENHANCED GOALS WITH NETWORK CONNECTIONS
-- ============================================================================

-- Enhanced Goals with network awareness
CREATE TABLE IF NOT EXISTS goals (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Goal classification
  goal_type VARCHAR(50) NOT NULL DEFAULT 'performance', -- performance, development, career, project, team
  category VARCHAR(50) NOT NULL DEFAULT 'individual', -- individual, team, organizational
  
  -- Timeline and progress
  target_date DATE,
  status VARCHAR(50) NOT NULL DEFAULT 'not-started',
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  
  -- Network connections
  related_skills INTEGER[], -- Array of skill IDs this goal develops
  supporting_peers INTEGER[], -- Array of user IDs who can help
  dependent_goals INTEGER[], -- Array of goal IDs this depends on
  
  -- Success criteria and measurement
  success_criteria JSONB, -- Structured success metrics
  measurement_method VARCHAR(100),
  
  -- Support and resources
  resources_needed TEXT,
  manager_support_required TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Goal Dependencies and Networks
CREATE TABLE IF NOT EXISTS goal_dependencies (
  id SERIAL PRIMARY KEY,
  goal_id INTEGER NOT NULL REFERENCES goals(id) ON DELETE CASCADE,
  depends_on_goal_id INTEGER NOT NULL REFERENCES goals(id) ON DELETE CASCADE,
  dependency_type VARCHAR(50) NOT NULL, -- prerequisite, supporting, blocking, related
  dependency_strength VARCHAR(20) DEFAULT 'medium', -- weak, medium, strong
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(goal_id, depends_on_goal_id)
);

-- ============================================================================
-- TEAM TOPOLOGY AND ORGANIZATIONAL STRUCTURE
-- ============================================================================

-- Teams with Team Topologies classification
CREATE TABLE IF NOT EXISTS teams (
  id SERIAL PRIMARY KEY,
  name VARCHAR(150) NOT NULL,
  team_type VARCHAR(50) NOT NULL, -- stream-aligned, platform, enabling, complicated-subsystem
  purpose TEXT,
  domain_expertise TEXT[],
  cognitive_load_assessment INTEGER CHECK (cognitive_load_assessment >= 1 AND cognitive_load_assessment <= 10),
  
  -- Team health metrics
  autonomy_level INTEGER CHECK (autonomy_level >= 1 AND autonomy_level <= 10),
  mastery_level INTEGER CHECK (mastery_level >= 1 AND mastery_level <= 10),
  purpose_clarity INTEGER CHECK (purpose_clarity >= 1 AND purpose_clarity <= 10),
  
  -- Interaction patterns
  preferred_interaction_modes TEXT[], -- collaboration, x-as-a-service, facilitating
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Team Memberships
CREATE TABLE IF NOT EXISTS team_memberships (
  id SERIAL PRIMARY KEY,
  team_id INTEGER NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role_in_team VARCHAR(100),
  start_date DATE NOT NULL DEFAULT CURRENT_DATE,
  end_date DATE,
  contribution_level VARCHAR(20) DEFAULT 'full-time', -- full-time, part-time, consultant, advisor
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(team_id, employee_id, start_date)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_manager_id ON users(manager_id);
CREATE INDEX IF NOT EXISTS idx_users_team_type ON users(team_type);
CREATE INDEX IF NOT EXISTS idx_users_career_stage ON users(career_stage);

-- Skills indexes
CREATE INDEX IF NOT EXISTS idx_employee_skills_employee_id ON employee_skills(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_skills_skill_id ON employee_skills(skill_id);
CREATE INDEX IF NOT EXISTS idx_employee_skills_current_level ON employee_skills(current_level);

-- Interaction indexes
CREATE INDEX IF NOT EXISTS idx_manager_interactions_manager_id ON manager_interactions(manager_id);
CREATE INDEX IF NOT EXISTS idx_manager_interactions_employee_id ON manager_interactions(employee_id);
CREATE INDEX IF NOT EXISTS idx_manager_interactions_date ON manager_interactions(interaction_date);
CREATE INDEX IF NOT EXISTS idx_manager_interactions_type ON manager_interactions(interaction_type);

-- Goal indexes
CREATE INDEX IF NOT EXISTS idx_goals_employee_id ON goals(employee_id);
CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status);
CREATE INDEX IF NOT EXISTS idx_goals_goal_type ON goals(goal_type);
CREATE INDEX IF NOT EXISTS idx_goals_target_date ON goals(target_date);

-- Team indexes
CREATE INDEX IF NOT EXISTS idx_team_memberships_team_id ON team_memberships(team_id);
CREATE INDEX IF NOT EXISTS idx_team_memberships_employee_id ON team_memberships(employee_id);
