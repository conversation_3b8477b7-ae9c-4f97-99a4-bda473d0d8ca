import { connectDatabase, query } from '../config/database';
import { logger } from '../config/logger';
import { hashPassword } from '../utils/auth';

interface SeedUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
}

const seedUsers: SeedUser[] = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    firstName: '<PERSON>',
    lastName: 'Manager',
    role: 'manager'
  },
  {
    email: '<EMAIL>',
    password: 'Employee123!',
    firstName: 'Jane',
    lastName: 'Employee',
    role: 'employee'
  }
];

async function seedDatabase() {
  try {
    await connectDatabase();
    logger.info('Starting database seeding...');

    // Check if users already exist
    const existingUsers = await query('SELECT COUNT(*) as count FROM users');
    if (existingUsers.rows[0].count > 0) {
      logger.info('Database already contains users, skipping seed');
      return;
    }

    // Create seed users
    for (const user of seedUsers) {
      const hashedPassword = await hashPassword(user.password);
      
      await query(
        `INSERT INTO users (email, password, first_name, last_name, role) 
         VALUES ($1, $2, $3, $4, $5)`,
        [user.email, hashedPassword, user.firstName, user.lastName, user.role]
      );
      
      logger.info(`Created user: ${user.email} (${user.role})`);
    }

    // Get user IDs for creating sample data
    const users = await query('SELECT id, email, role FROM users ORDER BY id');
    const adminUser = users.rows.find((u: any) => u.role === 'admin');
    const managerUser = users.rows.find((u: any) => u.role === 'manager');
    const employeeUser = users.rows.find((u: any) => u.role === 'employee');

    // Create sample goals
    if (employeeUser) {
      await query(
        `INSERT INTO goals (employee_id, title, description, target_date, status, progress, category) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Complete TypeScript Training',
          'Finish the advanced TypeScript course and apply learnings to current projects',
          '2024-06-30',
          'in-progress',
          60,
          'development'
        ]
      );

      await query(
        `INSERT INTO goals (employee_id, title, description, target_date, status, progress, category) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Improve Code Review Skills',
          'Participate in more code reviews and provide constructive feedback',
          '2024-05-31',
          'not-started',
          0,
          'performance'
        ]
      );

      logger.info('Created sample goals');
    }

    // Create sample project
    await query(
      `INSERT INTO projects (name, description, start_date, end_date, status) 
       VALUES ($1, $2, $3, $4, $5)`,
      [
        'ConfidentCoaching Platform',
        'Development of the employee performance management platform',
        '2024-01-01',
        '2024-12-31',
        'in-progress'
      ]
    );

    const project = await query('SELECT id FROM projects WHERE name = $1', ['ConfidentCoaching Platform']);
    
    // Create project assignments
    if (project.rows.length > 0 && employeeUser && managerUser) {
      const projectId = project.rows[0].id;
      
      await query(
        `INSERT INTO project_assignments (project_id, employee_id, role, assigned_date, status) 
         VALUES ($1, $2, $3, $4, $5)`,
        [projectId, managerUser.id, 'Project Manager', '2024-01-01', 'active']
      );

      await query(
        `INSERT INTO project_assignments (project_id, employee_id, role, assigned_date, status) 
         VALUES ($1, $2, $3, $4, $5)`,
        [projectId, employeeUser.id, 'Developer', '2024-01-15', 'active']
      );

      logger.info('Created sample project and assignments');
    }

    // Create sample training record
    if (employeeUser) {
      await query(
        `INSERT INTO trainings (employee_id, title, description, training_date, duration_hours, provider, status) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Advanced TypeScript Workshop',
          'Comprehensive workshop covering advanced TypeScript features and best practices',
          '2024-03-15',
          8.0,
          'Tech Training Institute',
          'completed'
        ]
      );

      logger.info('Created sample training record');
    }

    // Create sample topic
    if (managerUser) {
      await query(
        `INSERT INTO topics (title, description, created_by, category, priority, status) 
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          'Q1 Performance Review Process',
          'Discussion about the upcoming Q1 performance review process and timeline',
          managerUser.id,
          'Performance Management',
          'high',
          'open'
        ]
      );

      logger.info('Created sample topic');
    }

    // Create network-based data
    await seedNetworkData(users.rows);

    logger.info('Database seeding completed successfully');

    // Log the created accounts for reference
    logger.info('=== SEED ACCOUNTS CREATED ===');
    logger.info('Admin: <EMAIL> / Admin123!');
    logger.info('Manager: <EMAIL> / Manager123!');
    logger.info('Employee: <EMAIL> / Employee123!');
    logger.info('=============================');

  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      logger.info('Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding process failed:', error);
      process.exit(1);
    });
}

async function seedNetworkData(users: any[]) {
  try {
    const adminUser = users.find((u: any) => u.role === 'admin');
    const managerUser = users.find((u: any) => u.role === 'manager');
    const employeeUser = users.find((u: any) => u.role === 'employee');

    // Create core skills
    const skills = [
      { name: 'TypeScript', category: 'technical', type: 'hard' },
      { name: 'React', category: 'technical', type: 'hard' },
      { name: 'Leadership', category: 'leadership', type: 'soft' },
      { name: 'Communication', category: 'communication', type: 'soft' },
      { name: 'Project Management', category: 'leadership', type: 'hybrid' },
      { name: 'Mentoring', category: 'leadership', type: 'soft' },
      { name: 'Problem Solving', category: 'technical', type: 'hybrid' },
      { name: 'Team Collaboration', category: 'communication', type: 'soft' }
    ];

    for (const skill of skills) {
      await query(
        `INSERT INTO skills (name, category, skill_type, is_core_skill) VALUES ($1, $2, $3, $4)`,
        [skill.name, skill.category, skill.type, true]
      );
    }

    // Get skill IDs
    const skillsResult = await query('SELECT id, name FROM skills');
    const skillMap = new Map(skillsResult.rows.map((s: any) => [s.name, s.id]));

    // Create employee skills
    if (employeeUser) {
      const employeeSkills = [
        { skill: 'TypeScript', currentLevel: 'competent', targetLevel: 'proficient' },
        { skill: 'React', currentLevel: 'proficient', targetLevel: 'expert' },
        { skill: 'Communication', currentLevel: 'competent', targetLevel: 'proficient' },
        { skill: 'Problem Solving', currentLevel: 'proficient', targetLevel: 'expert' }
      ];

      for (const empSkill of employeeSkills) {
        const skillId = skillMap.get(empSkill.skill);
        if (skillId) {
          await query(
            `INSERT INTO employee_skills (employee_id, skill_id, current_level, target_level, development_priority)
             VALUES ($1, $2, $3, $4, $5)`,
            [employeeUser.id, skillId, empSkill.currentLevel, empSkill.targetLevel, 'high']
          );
        }
      }
    }

    // Create manager skills
    if (managerUser) {
      const managerSkills = [
        { skill: 'Leadership', currentLevel: 'proficient', targetLevel: 'expert' },
        { skill: 'Project Management', currentLevel: 'expert', targetLevel: 'expert' },
        { skill: 'Mentoring', currentLevel: 'competent', targetLevel: 'proficient' },
        { skill: 'Communication', currentLevel: 'proficient', targetLevel: 'expert' }
      ];

      for (const mgSkill of managerSkills) {
        const skillId = skillMap.get(mgSkill.skill);
        if (skillId) {
          await query(
            `INSERT INTO employee_skills (employee_id, skill_id, current_level, target_level, development_priority)
             VALUES ($1, $2, $3, $4, $5)`,
            [managerUser.id, skillId, mgSkill.currentLevel, mgSkill.targetLevel, 'medium']
          );
        }
      }
    }

    // Create career aspirations
    if (employeeUser) {
      await query(
        `INSERT INTO career_aspirations (employee_id, aspiration_type, target_role, target_timeline, motivation, probability_score)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          employeeUser.id,
          'role',
          'Senior Software Engineer',
          '1-year',
          'Want to take on more technical leadership responsibilities and mentor junior developers',
          7
        ]
      );
    }

    // Create manager interactions
    if (managerUser && employeeUser) {
      await query(
        `INSERT INTO manager_interactions (
          manager_id, employee_id, interaction_type, interaction_date, duration_minutes,
          topics_discussed, employee_mood, engagement_level, wins_celebrated,
          challenges_discussed, manager_notes, follow_up_required
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`,
        [
          managerUser.id,
          employeeUser.id,
          'one-on-one',
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
          45,
          ['Goal progress', 'Career development', 'Team collaboration'],
          'motivated',
          8,
          'Successfully completed TypeScript training module',
          'Struggling with time management on multiple projects',
          'Employee is highly engaged and motivated. Needs support with prioritization.',
          true
        ]
      );
    }

    // Create peer relationships
    if (managerUser && employeeUser) {
      await query(
        `INSERT INTO peer_relationships (
          employee_id, peer_id, relationship_type, relationship_strength,
          collaboration_frequency, knowledge_flow_direction, relationship_quality
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          managerUser.id,
          'mentor',
          'strong',
          'weekly',
          'from-peer',
          8
        ]
      );
    }

    // Create a team
    await query(
      `INSERT INTO teams (name, team_type, purpose, domain_expertise, cognitive_load_assessment)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        'Platform Engineering',
        'platform',
        'Provide internal development tools and infrastructure to reduce cognitive load for product teams',
        ['DevOps', 'Infrastructure', 'Developer Tools'],
        6
      ]
    );

    const teamResult = await query('SELECT id FROM teams WHERE name = $1', ['Platform Engineering']);
    if (teamResult.rows.length > 0 && employeeUser && managerUser) {
      const teamId = teamResult.rows[0].id;

      // Add team memberships
      await query(
        `INSERT INTO team_memberships (team_id, employee_id, role_in_team, contribution_level)
         VALUES ($1, $2, $3, $4)`,
        [teamId, managerUser.id, 'Team Lead', 'full-time']
      );

      await query(
        `INSERT INTO team_memberships (team_id, employee_id, role_in_team, contribution_level)
         VALUES ($1, $2, $3, $4)`,
        [teamId, employeeUser.id, 'Software Engineer', 'full-time']
      );
    }

    logger.info('Network-based seed data created successfully');
  } catch (error) {
    logger.error('Error creating network seed data:', error);
  }
}

export { seedDatabase };
