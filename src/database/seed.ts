import { connectDatabase, query } from '../config/database';
import { logger } from '../config/logger';
import { hashPassword } from '../utils/auth';

interface SeedUser {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
}

const seedUsers: SeedUser[] = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin'
  },
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    firstName: '<PERSON>',
    lastName: 'Manager',
    role: 'manager'
  },
  {
    email: '<EMAIL>',
    password: 'Employee123!',
    firstName: 'Jane',
    lastName: 'Employee',
    role: 'employee'
  }
];

async function seedDatabase() {
  try {
    await connectDatabase();
    logger.info('Starting database seeding...');

    // Check if users already exist
    const existingUsers = await query('SELECT COUNT(*) as count FROM users');
    if (existingUsers.rows[0].count > 0) {
      logger.info('Database already contains users, skipping seed');
      return;
    }

    // Create seed users
    for (const user of seedUsers) {
      const hashedPassword = await hashPassword(user.password);
      
      await query(
        `INSERT INTO users (email, password, first_name, last_name, role) 
         VALUES ($1, $2, $3, $4, $5)`,
        [user.email, hashedPassword, user.firstName, user.lastName, user.role]
      );
      
      logger.info(`Created user: ${user.email} (${user.role})`);
    }

    // Get user IDs for creating sample data
    const users = await query('SELECT id, email, role FROM users ORDER BY id');
    const adminUser = users.rows.find((u: any) => u.role === 'admin');
    const managerUser = users.rows.find((u: any) => u.role === 'manager');
    const employeeUser = users.rows.find((u: any) => u.role === 'employee');

    // Create sample goals
    if (employeeUser) {
      await query(
        `INSERT INTO goals (employee_id, title, description, target_date, status, progress, category) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Complete TypeScript Training',
          'Finish the advanced TypeScript course and apply learnings to current projects',
          '2024-06-30',
          'in-progress',
          60,
          'development'
        ]
      );

      await query(
        `INSERT INTO goals (employee_id, title, description, target_date, status, progress, category) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Improve Code Review Skills',
          'Participate in more code reviews and provide constructive feedback',
          '2024-05-31',
          'not-started',
          0,
          'performance'
        ]
      );

      logger.info('Created sample goals');
    }

    // Create sample project
    await query(
      `INSERT INTO projects (name, description, start_date, end_date, status) 
       VALUES ($1, $2, $3, $4, $5)`,
      [
        'ConfidentCoaching Platform',
        'Development of the employee performance management platform',
        '2024-01-01',
        '2024-12-31',
        'in-progress'
      ]
    );

    const project = await query('SELECT id FROM projects WHERE name = $1', ['ConfidentCoaching Platform']);
    
    // Create project assignments
    if (project.rows.length > 0 && employeeUser && managerUser) {
      const projectId = project.rows[0].id;
      
      await query(
        `INSERT INTO project_assignments (project_id, employee_id, role, assigned_date, status) 
         VALUES ($1, $2, $3, $4, $5)`,
        [projectId, managerUser.id, 'Project Manager', '2024-01-01', 'active']
      );

      await query(
        `INSERT INTO project_assignments (project_id, employee_id, role, assigned_date, status) 
         VALUES ($1, $2, $3, $4, $5)`,
        [projectId, employeeUser.id, 'Developer', '2024-01-15', 'active']
      );

      logger.info('Created sample project and assignments');
    }

    // Create sample training record
    if (employeeUser) {
      await query(
        `INSERT INTO trainings (employee_id, title, description, training_date, duration_hours, provider, status) 
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          employeeUser.id,
          'Advanced TypeScript Workshop',
          'Comprehensive workshop covering advanced TypeScript features and best practices',
          '2024-03-15',
          8.0,
          'Tech Training Institute',
          'completed'
        ]
      );

      logger.info('Created sample training record');
    }

    // Create sample topic
    if (managerUser) {
      await query(
        `INSERT INTO topics (title, description, created_by, category, priority, status) 
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          'Q1 Performance Review Process',
          'Discussion about the upcoming Q1 performance review process and timeline',
          managerUser.id,
          'Performance Management',
          'high',
          'open'
        ]
      );

      logger.info('Created sample topic');
    }

    logger.info('Database seeding completed successfully');
    
    // Log the created accounts for reference
    logger.info('=== SEED ACCOUNTS CREATED ===');
    logger.info('Admin: <EMAIL> / Admin123!');
    logger.info('Manager: <EMAIL> / Manager123!');
    logger.info('Employee: <EMAIL> / Employee123!');
    logger.info('=============================');

  } catch (error) {
    logger.error('Database seeding failed:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      logger.info('Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeding process failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
