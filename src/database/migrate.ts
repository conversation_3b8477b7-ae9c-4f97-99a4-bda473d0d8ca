import { connectDatabase, query } from '../config/database';
import { logger } from '../config/logger';

interface Migration {
  id: string;
  name: string;
  up: string;
  down: string;
}

const migrations: Migration[] = [
  {
    id: '001',
    name: 'create_users_table',
    up: `
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VA<PERSON>HA<PERSON>(100),
        last_name VA<PERSON><PERSON><PERSON>(100),
        role VARCHAR(50) NOT NULL DEFAULT 'employee',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    `,
    down: `
      DROP TABLE IF EXISTS users;
    `
  },
  {
    id: '002',
    name: 'create_goals_table',
    up: `
      CREATE TABLE IF NOT EXISTS goals (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        target_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'not-started',
        progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
        category VARCHAR(50) NOT NULL DEFAULT 'performance',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_goals_employee_id ON goals(employee_id);
      CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status);
      CREATE INDEX IF NOT EXISTS idx_goals_category ON goals(category);
    `,
    down: `
      DROP TABLE IF EXISTS goals;
    `
  },
  {
    id: '003',
    name: 'create_performance_reviews_table',
    up: `
      CREATE TABLE IF NOT EXISTS performance_reviews (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        manager_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        review_date DATE NOT NULL,
        review_period_start DATE,
        review_period_end DATE,
        overall_rating VARCHAR(50),
        strengths TEXT,
        areas_for_improvement TEXT,
        comments TEXT,
        employee_comments TEXT,
        status VARCHAR(50) NOT NULL DEFAULT 'draft',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_employee_id ON performance_reviews(employee_id);
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_manager_id ON performance_reviews(manager_id);
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_status ON performance_reviews(status);
    `,
    down: `
      DROP TABLE IF EXISTS performance_reviews;
    `
  },
  {
    id: '004',
    name: 'create_trainings_table',
    up: `
      CREATE TABLE IF NOT EXISTS trainings (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        training_date DATE,
        duration_hours DECIMAL(5,2),
        provider VARCHAR(255),
        status VARCHAR(50) NOT NULL DEFAULT 'scheduled',
        completion_date DATE,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_trainings_employee_id ON trainings(employee_id);
      CREATE INDEX IF NOT EXISTS idx_trainings_status ON trainings(status);
    `,
    down: `
      DROP TABLE IF EXISTS trainings;
    `
  },
  {
    id: '005',
    name: 'create_topics_table',
    up: `
      CREATE TABLE IF NOT EXISTS topics (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        category VARCHAR(100),
        priority VARCHAR(50) DEFAULT 'medium',
        status VARCHAR(50) NOT NULL DEFAULT 'open',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_topics_created_by ON topics(created_by);
      CREATE INDEX IF NOT EXISTS idx_topics_status ON topics(status);
      CREATE INDEX IF NOT EXISTS idx_topics_category ON topics(category);
    `,
    down: `
      DROP TABLE IF EXISTS topics;
    `
  },
  {
    id: '006',
    name: 'create_projects_table',
    up: `
      CREATE TABLE IF NOT EXISTS projects (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        start_date DATE,
        end_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'not-started',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
    `,
    down: `
      DROP TABLE IF EXISTS projects;
    `
  },
  {
    id: '007',
    name: 'create_project_assignments_table',
    up: `
      CREATE TABLE IF NOT EXISTS project_assignments (
        id SERIAL PRIMARY KEY,
        project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role VARCHAR(100),
        assigned_date DATE NOT NULL DEFAULT CURRENT_DATE,
        start_date DATE,
        end_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'assigned',
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(project_id, employee_id)
      );
      
      CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON project_assignments(project_id);
      CREATE INDEX IF NOT EXISTS idx_project_assignments_employee_id ON project_assignments(employee_id);
      CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON project_assignments(status);
    `,
    down: `
      DROP TABLE IF EXISTS project_assignments;
    `
  },
  {
    id: '008',
    name: 'create_migrations_table',
    up: `
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(10) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `,
    down: `
      DROP TABLE IF EXISTS migrations;
    `
  },
  {
    id: '009',
    name: 'enhance_users_for_network_management',
    up: `
      -- Add network-based management fields to users table
      ALTER TABLE users ADD COLUMN IF NOT EXISTS employee_id VARCHAR(50) UNIQUE;
      ALTER TABLE users ADD COLUMN IF NOT EXISTS department VARCHAR(100);
      ALTER TABLE users ADD COLUMN IF NOT EXISTS job_title VARCHAR(150);
      ALTER TABLE users ADD COLUMN IF NOT EXISTS hire_date DATE;
      ALTER TABLE users ADD COLUMN IF NOT EXISTS manager_id INTEGER REFERENCES users(id);
      ALTER TABLE users ADD COLUMN IF NOT EXISTS team_type VARCHAR(50) DEFAULT 'stream-aligned';
      ALTER TABLE users ADD COLUMN IF NOT EXISTS cognitive_load_level INTEGER DEFAULT 5 CHECK (cognitive_load_level >= 1 AND cognitive_load_level <= 10);
      ALTER TABLE users ADD COLUMN IF NOT EXISTS preferred_communication_style VARCHAR(50) DEFAULT 'collaborative';
      ALTER TABLE users ADD COLUMN IF NOT EXISTS career_stage VARCHAR(50) DEFAULT 'developing';

      -- Add indexes for new fields
      CREATE INDEX IF NOT EXISTS idx_users_manager_id ON users(manager_id);
      CREATE INDEX IF NOT EXISTS idx_users_team_type ON users(team_type);
      CREATE INDEX IF NOT EXISTS idx_users_career_stage ON users(career_stage);
      CREATE INDEX IF NOT EXISTS idx_users_department ON users(department);
    `,
    down: `
      ALTER TABLE users DROP COLUMN IF EXISTS employee_id;
      ALTER TABLE users DROP COLUMN IF EXISTS department;
      ALTER TABLE users DROP COLUMN IF EXISTS job_title;
      ALTER TABLE users DROP COLUMN IF EXISTS hire_date;
      ALTER TABLE users DROP COLUMN IF EXISTS manager_id;
      ALTER TABLE users DROP COLUMN IF EXISTS team_type;
      ALTER TABLE users DROP COLUMN IF EXISTS cognitive_load_level;
      ALTER TABLE users DROP COLUMN IF EXISTS preferred_communication_style;
      ALTER TABLE users DROP COLUMN IF EXISTS career_stage;
    `
  },
  {
    id: '010',
    name: 'create_skills_and_competencies',
    up: `
      -- Skills framework
      CREATE TABLE IF NOT EXISTS skills (
        id SERIAL PRIMARY KEY,
        name VARCHAR(150) NOT NULL UNIQUE,
        category VARCHAR(100) NOT NULL,
        skill_type VARCHAR(50) NOT NULL,
        description TEXT,
        proficiency_levels JSONB DEFAULT '["novice", "advanced-beginner", "competent", "proficient", "expert"]',
        is_core_skill BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Employee skills with proficiency tracking
      CREATE TABLE IF NOT EXISTS employee_skills (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        skill_id INTEGER NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
        current_level VARCHAR(50) NOT NULL DEFAULT 'novice',
        target_level VARCHAR(50),
        last_assessed_date DATE,
        assessed_by INTEGER REFERENCES users(id),
        evidence_notes TEXT,
        development_priority VARCHAR(20) DEFAULT 'medium',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(employee_id, skill_id)
      );

      -- Career aspirations
      CREATE TABLE IF NOT EXISTS career_aspirations (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        aspiration_type VARCHAR(50) NOT NULL,
        target_role VARCHAR(150),
        target_department VARCHAR(100),
        target_timeline VARCHAR(50),
        motivation TEXT,
        current_gap_analysis TEXT,
        development_actions TEXT,
        probability_score INTEGER CHECK (probability_score >= 1 AND probability_score <= 10),
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_skills_category ON skills(category);
      CREATE INDEX IF NOT EXISTS idx_skills_skill_type ON skills(skill_type);
      CREATE INDEX IF NOT EXISTS idx_employee_skills_employee_id ON employee_skills(employee_id);
      CREATE INDEX IF NOT EXISTS idx_employee_skills_skill_id ON employee_skills(skill_id);
      CREATE INDEX IF NOT EXISTS idx_employee_skills_current_level ON employee_skills(current_level);
      CREATE INDEX IF NOT EXISTS idx_career_aspirations_employee_id ON career_aspirations(employee_id);
      CREATE INDEX IF NOT EXISTS idx_career_aspirations_status ON career_aspirations(status);
    `,
    down: `
      DROP TABLE IF EXISTS career_aspirations;
      DROP TABLE IF EXISTS employee_skills;
      DROP TABLE IF EXISTS skills;
    `
  },
  {
    id: '011',
    name: 'create_manager_interactions',
    up: `
      -- Manager-employee interactions with rich context
      CREATE TABLE IF NOT EXISTS manager_interactions (
        id SERIAL PRIMARY KEY,
        manager_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        interaction_type VARCHAR(50) NOT NULL,
        interaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
        duration_minutes INTEGER,
        location VARCHAR(100),

        -- Structured interaction data
        topics_discussed TEXT[],
        employee_mood VARCHAR(50),
        engagement_level INTEGER CHECK (engagement_level >= 1 AND engagement_level <= 10),

        -- Key conversation elements
        wins_celebrated TEXT,
        challenges_discussed TEXT,
        support_requested TEXT,
        actions_agreed JSONB,

        -- Manager observations
        manager_notes TEXT,
        development_opportunities_identified TEXT,
        follow_up_required BOOLEAN DEFAULT false,
        follow_up_date DATE,

        -- Relationship building
        personal_updates TEXT,
        trust_building_notes TEXT,

        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Peer relationships and collaboration networks
      CREATE TABLE IF NOT EXISTS peer_relationships (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        peer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        relationship_type VARCHAR(50) NOT NULL,
        relationship_strength VARCHAR(20) DEFAULT 'weak',
        collaboration_frequency VARCHAR(30) DEFAULT 'occasional',
        knowledge_flow_direction VARCHAR(20) DEFAULT 'bidirectional',

        -- Relationship context
        context TEXT,
        shared_projects INTEGER[],
        shared_skills INTEGER[],

        -- Relationship health
        last_interaction_date DATE,
        relationship_quality INTEGER CHECK (relationship_quality >= 1 AND relationship_quality <= 10),

        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(employee_id, peer_id)
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_manager_interactions_manager_id ON manager_interactions(manager_id);
      CREATE INDEX IF NOT EXISTS idx_manager_interactions_employee_id ON manager_interactions(employee_id);
      CREATE INDEX IF NOT EXISTS idx_manager_interactions_date ON manager_interactions(interaction_date);
      CREATE INDEX IF NOT EXISTS idx_manager_interactions_type ON manager_interactions(interaction_type);
      CREATE INDEX IF NOT EXISTS idx_peer_relationships_employee_id ON peer_relationships(employee_id);
      CREATE INDEX IF NOT EXISTS idx_peer_relationships_peer_id ON peer_relationships(peer_id);
      CREATE INDEX IF NOT EXISTS idx_peer_relationships_type ON peer_relationships(relationship_type);
    `,
    down: `
      DROP TABLE IF EXISTS peer_relationships;
      DROP TABLE IF EXISTS manager_interactions;
    `
  },
  {
    id: '012',
    name: 'enhance_goals_for_network',
    up: `
      -- Enhance goals table with network connections
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS goal_type VARCHAR(50) NOT NULL DEFAULT 'performance';
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS related_skills INTEGER[];
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS supporting_peers INTEGER[];
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS dependent_goals INTEGER[];
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS success_criteria JSONB;
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS measurement_method VARCHAR(100);
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS resources_needed TEXT;
      ALTER TABLE goals ADD COLUMN IF NOT EXISTS manager_support_required TEXT;

      -- Goal dependencies
      CREATE TABLE IF NOT EXISTS goal_dependencies (
        id SERIAL PRIMARY KEY,
        goal_id INTEGER NOT NULL REFERENCES goals(id) ON DELETE CASCADE,
        depends_on_goal_id INTEGER NOT NULL REFERENCES goals(id) ON DELETE CASCADE,
        dependency_type VARCHAR(50) NOT NULL,
        dependency_strength VARCHAR(20) DEFAULT 'medium',
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(goal_id, depends_on_goal_id)
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_goals_goal_type ON goals(goal_type);
      CREATE INDEX IF NOT EXISTS idx_goal_dependencies_goal_id ON goal_dependencies(goal_id);
      CREATE INDEX IF NOT EXISTS idx_goal_dependencies_depends_on ON goal_dependencies(depends_on_goal_id);
    `,
    down: `
      DROP TABLE IF EXISTS goal_dependencies;
      ALTER TABLE goals DROP COLUMN IF EXISTS goal_type;
      ALTER TABLE goals DROP COLUMN IF EXISTS related_skills;
      ALTER TABLE goals DROP COLUMN IF EXISTS supporting_peers;
      ALTER TABLE goals DROP COLUMN IF EXISTS dependent_goals;
      ALTER TABLE goals DROP COLUMN IF EXISTS success_criteria;
      ALTER TABLE goals DROP COLUMN IF EXISTS measurement_method;
      ALTER TABLE goals DROP COLUMN IF EXISTS resources_needed;
      ALTER TABLE goals DROP COLUMN IF EXISTS manager_support_required;
    `
  },
  {
    id: '013',
    name: 'create_teams_and_topology',
    up: `
      -- Teams with Team Topologies classification
      CREATE TABLE IF NOT EXISTS teams (
        id SERIAL PRIMARY KEY,
        name VARCHAR(150) NOT NULL,
        team_type VARCHAR(50) NOT NULL,
        purpose TEXT,
        domain_expertise TEXT[],
        cognitive_load_assessment INTEGER CHECK (cognitive_load_assessment >= 1 AND cognitive_load_assessment <= 10),

        -- Team health metrics
        autonomy_level INTEGER CHECK (autonomy_level >= 1 AND autonomy_level <= 10),
        mastery_level INTEGER CHECK (mastery_level >= 1 AND mastery_level <= 10),
        purpose_clarity INTEGER CHECK (purpose_clarity >= 1 AND purpose_clarity <= 10),

        -- Interaction patterns
        preferred_interaction_modes TEXT[],

        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Team memberships
      CREATE TABLE IF NOT EXISTS team_memberships (
        id SERIAL PRIMARY KEY,
        team_id INTEGER NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role_in_team VARCHAR(100),
        start_date DATE NOT NULL DEFAULT CURRENT_DATE,
        end_date DATE,
        contribution_level VARCHAR(20) DEFAULT 'full-time',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(team_id, employee_id, start_date)
      );

      -- Interaction flows for structured management processes
      CREATE TABLE IF NOT EXISTS interaction_flows (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        flow_type VARCHAR(50) NOT NULL,
        participant_ids INTEGER[],
        current_stage VARCHAR(100),
        next_stage_date TIMESTAMP WITH TIME ZONE,
        flow_data JSONB,
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );

      -- Indexes
      CREATE INDEX IF NOT EXISTS idx_teams_team_type ON teams(team_type);
      CREATE INDEX IF NOT EXISTS idx_team_memberships_team_id ON team_memberships(team_id);
      CREATE INDEX IF NOT EXISTS idx_team_memberships_employee_id ON team_memberships(employee_id);
      CREATE INDEX IF NOT EXISTS idx_interaction_flows_type ON interaction_flows(flow_type);
      CREATE INDEX IF NOT EXISTS idx_interaction_flows_status ON interaction_flows(status);
    `,
    down: `
      DROP TABLE IF EXISTS interaction_flows;
      DROP TABLE IF EXISTS team_memberships;
      DROP TABLE IF EXISTS teams;
    `
  }
];

async function createMigrationsTable() {
  const migrationTableMigration = migrations.find(m => m.name === 'create_migrations_table');
  if (migrationTableMigration) {
    await query(migrationTableMigration.up);
  }
}

async function getExecutedMigrations(): Promise<string[]> {
  try {
    const result = await query('SELECT id FROM migrations ORDER BY id');
    return result.rows.map((row: any) => row.id);
  } catch (error) {
    return [];
  }
}

async function executeMigration(migration: Migration) {
  logger.info(`Executing migration: ${migration.id} - ${migration.name}`);
  
  try {
    await query('BEGIN');
    await query(migration.up);
    await query('INSERT INTO migrations (id, name) VALUES ($1, $2)', [migration.id, migration.name]);
    await query('COMMIT');
    
    logger.info(`Migration completed: ${migration.id} - ${migration.name}`);
  } catch (error) {
    await query('ROLLBACK');
    logger.error(`Migration failed: ${migration.id} - ${migration.name}`, error);
    throw error;
  }
}

export async function runMigrations() {
  try {
    await connectDatabase();
    await createMigrationsTable();
    
    const executedMigrations = await getExecutedMigrations();
    const pendingMigrations = migrations.filter(m => !executedMigrations.includes(m.id));
    
    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations');
      return;
    }
    
    logger.info(`Found ${pendingMigrations.length} pending migrations`);
    
    for (const migration of pendingMigrations) {
      await executeMigration(migration);
    }
    
    logger.info('All migrations completed successfully');
  } catch (error) {
    logger.error('Migration process failed:', error);
    throw error;
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migration process completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration process failed:', error);
      process.exit(1);
    });
}
