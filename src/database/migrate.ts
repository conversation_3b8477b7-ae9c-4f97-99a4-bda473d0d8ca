import { connectDatabase, query } from '../config/database';
import { logger } from '../config/logger';

interface Migration {
  id: string;
  name: string;
  up: string;
  down: string;
}

const migrations: Migration[] = [
  {
    id: '001',
    name: 'create_users_table',
    up: `
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VA<PERSON>HA<PERSON>(100),
        last_name VA<PERSON><PERSON><PERSON>(100),
        role VARCHAR(50) NOT NULL DEFAULT 'employee',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    `,
    down: `
      DROP TABLE IF EXISTS users;
    `
  },
  {
    id: '002',
    name: 'create_goals_table',
    up: `
      CREATE TABLE IF NOT EXISTS goals (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        target_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'not-started',
        progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
        category VARCHAR(50) NOT NULL DEFAULT 'performance',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_goals_employee_id ON goals(employee_id);
      CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status);
      CREATE INDEX IF NOT EXISTS idx_goals_category ON goals(category);
    `,
    down: `
      DROP TABLE IF EXISTS goals;
    `
  },
  {
    id: '003',
    name: 'create_performance_reviews_table',
    up: `
      CREATE TABLE IF NOT EXISTS performance_reviews (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        manager_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        review_date DATE NOT NULL,
        review_period_start DATE,
        review_period_end DATE,
        overall_rating VARCHAR(50),
        strengths TEXT,
        areas_for_improvement TEXT,
        comments TEXT,
        employee_comments TEXT,
        status VARCHAR(50) NOT NULL DEFAULT 'draft',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_employee_id ON performance_reviews(employee_id);
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_manager_id ON performance_reviews(manager_id);
      CREATE INDEX IF NOT EXISTS idx_performance_reviews_status ON performance_reviews(status);
    `,
    down: `
      DROP TABLE IF EXISTS performance_reviews;
    `
  },
  {
    id: '004',
    name: 'create_trainings_table',
    up: `
      CREATE TABLE IF NOT EXISTS trainings (
        id SERIAL PRIMARY KEY,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        training_date DATE,
        duration_hours DECIMAL(5,2),
        provider VARCHAR(255),
        status VARCHAR(50) NOT NULL DEFAULT 'scheduled',
        completion_date DATE,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_trainings_employee_id ON trainings(employee_id);
      CREATE INDEX IF NOT EXISTS idx_trainings_status ON trainings(status);
    `,
    down: `
      DROP TABLE IF EXISTS trainings;
    `
  },
  {
    id: '005',
    name: 'create_topics_table',
    up: `
      CREATE TABLE IF NOT EXISTS topics (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        category VARCHAR(100),
        priority VARCHAR(50) DEFAULT 'medium',
        status VARCHAR(50) NOT NULL DEFAULT 'open',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_topics_created_by ON topics(created_by);
      CREATE INDEX IF NOT EXISTS idx_topics_status ON topics(status);
      CREATE INDEX IF NOT EXISTS idx_topics_category ON topics(category);
    `,
    down: `
      DROP TABLE IF EXISTS topics;
    `
  },
  {
    id: '006',
    name: 'create_projects_table',
    up: `
      CREATE TABLE IF NOT EXISTS projects (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        start_date DATE,
        end_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'not-started',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
    `,
    down: `
      DROP TABLE IF EXISTS projects;
    `
  },
  {
    id: '007',
    name: 'create_project_assignments_table',
    up: `
      CREATE TABLE IF NOT EXISTS project_assignments (
        id SERIAL PRIMARY KEY,
        project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
        employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role VARCHAR(100),
        assigned_date DATE NOT NULL DEFAULT CURRENT_DATE,
        start_date DATE,
        end_date DATE,
        status VARCHAR(50) NOT NULL DEFAULT 'assigned',
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(project_id, employee_id)
      );
      
      CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON project_assignments(project_id);
      CREATE INDEX IF NOT EXISTS idx_project_assignments_employee_id ON project_assignments(employee_id);
      CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON project_assignments(status);
    `,
    down: `
      DROP TABLE IF EXISTS project_assignments;
    `
  },
  {
    id: '008',
    name: 'create_migrations_table',
    up: `
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(10) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `,
    down: `
      DROP TABLE IF EXISTS migrations;
    `
  }
];

async function createMigrationsTable() {
  const migrationTableMigration = migrations.find(m => m.name === 'create_migrations_table');
  if (migrationTableMigration) {
    await query(migrationTableMigration.up);
  }
}

async function getExecutedMigrations(): Promise<string[]> {
  try {
    const result = await query('SELECT id FROM migrations ORDER BY id');
    return result.rows.map((row: any) => row.id);
  } catch (error) {
    return [];
  }
}

async function executeMigration(migration: Migration) {
  logger.info(`Executing migration: ${migration.id} - ${migration.name}`);
  
  try {
    await query('BEGIN');
    await query(migration.up);
    await query('INSERT INTO migrations (id, name) VALUES ($1, $2)', [migration.id, migration.name]);
    await query('COMMIT');
    
    logger.info(`Migration completed: ${migration.id} - ${migration.name}`);
  } catch (error) {
    await query('ROLLBACK');
    logger.error(`Migration failed: ${migration.id} - ${migration.name}`, error);
    throw error;
  }
}

export async function runMigrations() {
  try {
    await connectDatabase();
    await createMigrationsTable();
    
    const executedMigrations = await getExecutedMigrations();
    const pendingMigrations = migrations.filter(m => !executedMigrations.includes(m.id));
    
    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations');
      return;
    }
    
    logger.info(`Found ${pendingMigrations.length} pending migrations`);
    
    for (const migration of pendingMigrations) {
      await executeMigration(migration);
    }
    
    logger.info('All migrations completed successfully');
  } catch (error) {
    logger.error('Migration process failed:', error);
    throw error;
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migration process completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration process failed:', error);
      process.exit(1);
    });
}
