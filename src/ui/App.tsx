import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Dashboard } from './pages/Dashboard';
import { Login } from './pages/Login';
import { Goals } from './pages/Goals';
import { PerformanceReviews } from './pages/PerformanceReviews';
import { Training } from './pages/Training';
import { Projects } from './pages/Projects';
import { Topics } from './pages/Topics';
import { Layout } from './components/Layout';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/goals" element={<Goals />} />
                    <Route path="/performance-reviews" element={<PerformanceReviews />} />
                    <Route path="/training" element={<Training />} />
                    <Route path="/projects" element={<Projects />} />
                    <Route path="/topics" element={<Topics />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
