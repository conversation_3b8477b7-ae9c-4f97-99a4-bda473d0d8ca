import React, { useState, useEffect } from 'react';
import { Training } from '../../types/Training';
import { TrainingService } from '../../services/TrainingService';

interface TrainingListProps {
  employeeId: number;
}

const TrainingList: React.FC<TrainingListProps> = ({ employeeId }) => {
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrainings = async () => {
      try {
        setLoading(true);
        const trainingService = new TrainingService();
        const employeeTrainings = await trainingService.getEmployeeTrainings(employeeId);
        setTrainings(employeeTrainings);
        setError(null);
      } catch (err) {
        console.error('Error fetching trainings:', err);
        setError('Failed to load training records. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTrainings();
  }, [employeeId]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'planned':
        return 'badge bg-secondary';
      case 'in-progress':
        return 'badge bg-primary';
      case 'completed':
        return 'badge bg-success';
      case 'canceled':
        return 'badge bg-danger';
      default:
        return 'badge bg-secondary';
    }
  };

  const getTypeLabel = (type: string): string => {
    switch (type) {
      case 'course':
        return 'Course';
      case 'workshop':
        return 'Workshop';
      case 'conference':
        return 'Conference';
      case 'certification':
        return 'Certification';
      case 'other':
        return 'Other';
      default:
        return type;
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading training records...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (trainings.length === 0) {
    return <div className="alert alert-info">No training records found for this employee.</div>;
  }

  return (
    <div className="training-list">
      <h3>Employee Training Records</h3>
      <div className="list-group">
        {trainings.map((training) => (
          <div key={training.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">{training.title}</h5>
              <span className={getStatusBadgeClass(training.status)}>{training.status}</span>
            </div>
            <p className="mb-1">{training.description}</p>
            <div className="d-flex justify-content-between align-items-center">
              <span className="badge bg-info">{getTypeLabel(training.type)}</span>
              {training.provider && <small>Provider: {training.provider}</small>}
            </div>
            {(training.startDate || training.endDate) && (
              <div className="mt-2">
                {training.startDate && (
                  <small className="me-3">
                    Start: {new Date(training.startDate).toLocaleDateString()}
                  </small>
                )}
                {training.endDate && (
                  <small>
                    End: {new Date(training.endDate).toLocaleDateString()}
                  </small>
                )}
              </div>
            )}
            {training.completionCertificate && (
              <div className="mt-2">
                <a href={training.completionCertificate} target="_blank" rel="noopener noreferrer">
                  View Certificate
                </a>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TrainingList; 