import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { PerformanceReview, CreatePerformanceReviewData, UpdatePerformanceReviewData } from '../../types/PerformanceReview';
import { PerformanceReviewService } from '../../services/PerformanceReviewService';

// Schema for validating performance review data
const reviewSchema = z.object({
  employeeId: z.number().positive('Employee ID is required'),
  managerId: z.number().positive('Manager ID is required'),
  reviewDate: z.string().min(1, 'Review date is required'),
  reviewPeriodStart: z.string().optional(),
  reviewPeriodEnd: z.string().optional(),
  overallRating: z.string().optional(),
  strengths: z.string().optional(),
  areasForImprovement: z.string().optional(),
  comments: z.string().optional(),
  employeeComments: z.string().optional(),
  status: z.enum(['draft', 'in-review', 'completed'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
});

interface PerformanceReviewFormProps {
  review?: PerformanceReview;
  employeeId?: number;
  managerId?: number;
  onSave: (reviewId: number) => void;
  onCancel: () => void;
}

const PerformanceReviewForm: React.FC<PerformanceReviewFormProps> = ({ 
  review, 
  employeeId, 
  managerId, 
  onSave, 
  onCancel 
}) => {
  const isEditMode = !!review;
  
  const [formData, setFormData] = useState<CreatePerformanceReviewData | UpdatePerformanceReviewData>({
    employeeId: review?.employeeId || employeeId || 0,
    managerId: review?.managerId || managerId || 0,
    reviewDate: review?.reviewDate || new Date().toISOString().split('T')[0],
    reviewPeriodStart: review?.reviewPeriodStart || '',
    reviewPeriodEnd: review?.reviewPeriodEnd || '',
    overallRating: review?.overallRating || '',
    strengths: review?.strengths || '',
    areasForImprovement: review?.areasForImprovement || '',
    comments: review?.comments || '',
    employeeComments: review?.employeeComments || '',
    status: review?.status || 'draft',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Reset form when review, employeeId, or managerId changes
  useEffect(() => {
    setFormData({
      employeeId: review?.employeeId || employeeId || 0,
      managerId: review?.managerId || managerId || 0,
      reviewDate: review?.reviewDate || new Date().toISOString().split('T')[0],
      reviewPeriodStart: review?.reviewPeriodStart || '',
      reviewPeriodEnd: review?.reviewPeriodEnd || '',
      overallRating: review?.overallRating || '',
      strengths: review?.strengths || '',
      areasForImprovement: review?.areasForImprovement || '',
      comments: review?.comments || '',
      employeeComments: review?.employeeComments || '',
      status: review?.status || 'draft',
    });
    setErrors({});
    setSubmitError(null);
  }, [review, employeeId, managerId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle number inputs
    if (name === 'employeeId' || name === 'managerId') {
      setFormData({
        ...formData,
        [name]: parseInt(value) || undefined,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateForm = (): boolean => {
    try {
      // For edit mode, we need to validate only the fields that are present
      if (isEditMode) {
        const partialSchema = reviewSchema.partial();
        partialSchema.parse(formData);
      } else {
        reviewSchema.parse(formData);
      }
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      const reviewService = new PerformanceReviewService();
      
      let reviewId: number;
      
      if (isEditMode && review) {
        // Update existing review
        await reviewService.updatePerformanceReview(review.id, formData as UpdatePerformanceReviewData);
        reviewId = review.id;
      } else {
        // Create new review
        reviewId = await reviewService.createPerformanceReview(formData as CreatePerformanceReviewData);
      }
      
      onSave(reviewId);
    } catch (error) {
      console.error('Error saving performance review:', error);
      setSubmitError('Failed to save performance review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="performance-review-form">
      <h3>{isEditMode ? 'Edit Performance Review' : 'Create New Performance Review'}</h3>
      
      {submitError && (
        <div className="alert alert-danger">{submitError}</div>
      )}
      
      <form onSubmit={handleSubmit}>
        {!isEditMode && (
          <>
            <div className="mb-3">
              <label htmlFor="employeeId" className="form-label">Employee ID</label>
              <input
                type="number"
                className={`form-control ${errors.employeeId ? 'is-invalid' : ''}`}
                id="employeeId"
                name="employeeId"
                value={formData.employeeId || ''}
                onChange={handleChange}
                disabled={isEditMode || !!employeeId}
                required
              />
              {errors.employeeId && (
                <div className="invalid-feedback">{errors.employeeId}</div>
              )}
            </div>
            
            <div className="mb-3">
              <label htmlFor="managerId" className="form-label">Manager ID</label>
              <input
                type="number"
                className={`form-control ${errors.managerId ? 'is-invalid' : ''}`}
                id="managerId"
                name="managerId"
                value={formData.managerId || ''}
                onChange={handleChange}
                disabled={isEditMode || !!managerId}
                required
              />
              {errors.managerId && (
                <div className="invalid-feedback">{errors.managerId}</div>
              )}
            </div>
          </>
        )}
        
        <div className="mb-3">
          <label htmlFor="reviewDate" className="form-label">Review Date</label>
          <input
            type="date"
            className={`form-control ${errors.reviewDate ? 'is-invalid' : ''}`}
            id="reviewDate"
            name="reviewDate"
            value={formData.reviewDate || ''}
            onChange={handleChange}
            required
          />
          {errors.reviewDate && (
            <div className="invalid-feedback">{errors.reviewDate}</div>
          )}
        </div>
        
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="reviewPeriodStart" className="form-label">Period Start</label>
            <input
              type="date"
              className={`form-control ${errors.reviewPeriodStart ? 'is-invalid' : ''}`}
              id="reviewPeriodStart"
              name="reviewPeriodStart"
              value={formData.reviewPeriodStart || ''}
              onChange={handleChange}
            />
            {errors.reviewPeriodStart && (
              <div className="invalid-feedback">{errors.reviewPeriodStart}</div>
            )}
          </div>
          
          <div className="col-md-6 mb-3">
            <label htmlFor="reviewPeriodEnd" className="form-label">Period End</label>
            <input
              type="date"
              className={`form-control ${errors.reviewPeriodEnd ? 'is-invalid' : ''}`}
              id="reviewPeriodEnd"
              name="reviewPeriodEnd"
              value={formData.reviewPeriodEnd || ''}
              onChange={handleChange}
            />
            {errors.reviewPeriodEnd && (
              <div className="invalid-feedback">{errors.reviewPeriodEnd}</div>
            )}
          </div>
        </div>
        
        <div className="mb-3">
          <label htmlFor="overallRating" className="form-label">Overall Rating</label>
          <input
            type="text"
            className={`form-control ${errors.overallRating ? 'is-invalid' : ''}`}
            id="overallRating"
            name="overallRating"
            value={formData.overallRating || ''}
            onChange={handleChange}
            placeholder="e.g., Exceeds Expectations, Meets Expectations, etc."
          />
          {errors.overallRating && (
            <div className="invalid-feedback">{errors.overallRating}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="strengths" className="form-label">Strengths</label>
          <textarea
            className={`form-control ${errors.strengths ? 'is-invalid' : ''}`}
            id="strengths"
            name="strengths"
            value={formData.strengths || ''}
            onChange={handleChange}
            rows={3}
            placeholder="List the employee's key strengths and accomplishments"
          />
          {errors.strengths && (
            <div className="invalid-feedback">{errors.strengths}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="areasForImprovement" className="form-label">Areas for Improvement</label>
          <textarea
            className={`form-control ${errors.areasForImprovement ? 'is-invalid' : ''}`}
            id="areasForImprovement"
            name="areasForImprovement"
            value={formData.areasForImprovement || ''}
            onChange={handleChange}
            rows={3}
            placeholder="List areas where the employee can improve"
          />
          {errors.areasForImprovement && (
            <div className="invalid-feedback">{errors.areasForImprovement}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="comments" className="form-label">Manager Comments</label>
          <textarea
            className={`form-control ${errors.comments ? 'is-invalid' : ''}`}
            id="comments"
            name="comments"
            value={formData.comments || ''}
            onChange={handleChange}
            rows={3}
            placeholder="Additional comments from the manager"
          />
          {errors.comments && (
            <div className="invalid-feedback">{errors.comments}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="employeeComments" className="form-label">Employee Comments</label>
          <textarea
            className={`form-control ${errors.employeeComments ? 'is-invalid' : ''}`}
            id="employeeComments"
            name="employeeComments"
            value={formData.employeeComments || ''}
            onChange={handleChange}
            rows={3}
            placeholder="Comments from the employee"
          />
          {errors.employeeComments && (
            <div className="invalid-feedback">{errors.employeeComments}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="status" className="form-label">Status</label>
          <select
            className={`form-select ${errors.status ? 'is-invalid' : ''}`}
            id="status"
            name="status"
            value={formData.status || 'draft'}
            onChange={handleChange}
            required
          >
            <option value="draft">Draft</option>
            <option value="in-review">In Review</option>
            <option value="completed">Completed</option>
          </select>
          {errors.status && (
            <div className="invalid-feedback">{errors.status}</div>
          )}
        </div>
        
        <div className="d-flex justify-content-end gap-2">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : isEditMode ? 'Update Review' : 'Create Review'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PerformanceReviewForm; 