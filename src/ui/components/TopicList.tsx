import React, { useState, useEffect } from 'react';
import { Topic } from '../../types/Topic';
import { TopicService } from '../../services/TopicService';

interface TopicListProps {
  userId?: number;
  showAllTopics?: boolean;
}

const TopicList: React.FC<TopicListProps> = ({ userId, showAllTopics = false }) => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        const topicService = new TopicService();
        
        let topicData: Topic[] = [];
        
        if (showAllTopics) {
          topicData = await topicService.getAllTopics();
        } else if (userId) {
          topicData = await topicService.getUserTopics(userId);
        } else {
          throw new Error('Either userId must be provided or showAllTopics must be true');
        }
        
        setTopics(topicData);
        setError(null);
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError('Failed to load topics. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, [userId, showAllTopics]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'active':
        return 'badge bg-success';
      case 'completed':
        return 'badge bg-primary';
      case 'archived':
        return 'badge bg-secondary';
      default:
        return 'badge bg-secondary';
    }
  };

  const getCategoryBadgeClass = (category: string): string => {
    switch (category) {
      case 'technical':
        return 'badge bg-info';
      case 'soft-skills':
        return 'badge bg-warning';
      case 'project':
        return 'badge bg-primary';
      case 'career':
        return 'badge bg-success';
      case 'performance':
        return 'badge bg-danger';
      case 'other':
        return 'badge bg-secondary';
      default:
        return 'badge bg-secondary';
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading topics...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (topics.length === 0) {
    return <div className="alert alert-info">No topics found.</div>;
  }

  return (
    <div className="topic-list">
      <h3>Discussion Topics</h3>
      <div className="list-group">
        {topics.map((topic) => (
          <div key={topic.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">{topic.title}</h5>
              <span className={getStatusBadgeClass(topic.status)}>{topic.status}</span>
            </div>
            <p className="mb-1">{topic.description}</p>
            <div className="d-flex justify-content-between align-items-center">
              <span className={getCategoryBadgeClass(topic.category)}>{topic.category}</span>
              <small>
                Created by: {topic.creatorName || `User #${topic.createdBy}`}
              </small>
            </div>
            <small className="text-muted">
              Created: {new Date(topic.createdAt).toLocaleDateString()}
            </small>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopicList; 