import React, { useState, useEffect } from 'react';
import { PerformanceReview } from '../../types/PerformanceReview';
import { PerformanceReviewService } from '../../services/PerformanceReviewService';

interface PerformanceReviewListProps {
  employeeId?: number;
  managerId?: number;
}

const PerformanceReviewList: React.FC<PerformanceReviewListProps> = ({ employeeId, managerId }) => {
  const [reviews, setReviews] = useState<PerformanceReview[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        const reviewService = new PerformanceReviewService();
        
        let reviewData: PerformanceReview[] = [];
        
        if (employeeId) {
          reviewData = await reviewService.getEmployeePerformanceReviews(employeeId);
        } else if (managerId) {
          reviewData = await reviewService.getManagerPerformanceReviews(managerId);
        } else {
          throw new Error('Either employeeId or managerId must be provided');
        }
        
        setReviews(reviewData);
        setError(null);
      } catch (err) {
        console.error('Error fetching performance reviews:', err);
        setError('Failed to load performance reviews. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [employeeId, managerId]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'badge bg-secondary';
      case 'in-review':
        return 'badge bg-primary';
      case 'completed':
        return 'badge bg-success';
      default:
        return 'badge bg-secondary';
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading performance reviews...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (reviews.length === 0) {
    return <div className="alert alert-info">No performance reviews found.</div>;
  }

  return (
    <div className="performance-review-list">
      <h3>Performance Reviews</h3>
      <div className="list-group">
        {reviews.map((review) => (
          <div key={review.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">
                Review for {review.employeeName || `Employee #${review.employeeId}`}
              </h5>
              <span className={getStatusBadgeClass(review.status)}>{review.status}</span>
            </div>
            <p className="mb-1">
              <strong>Review Date:</strong> {new Date(review.reviewDate).toLocaleDateString()}
            </p>
            {review.reviewPeriodStart && review.reviewPeriodEnd && (
              <p className="mb-1">
                <strong>Period:</strong> {new Date(review.reviewPeriodStart).toLocaleDateString()} to {new Date(review.reviewPeriodEnd).toLocaleDateString()}
              </p>
            )}
            {review.overallRating && (
              <p className="mb-1">
                <strong>Overall Rating:</strong> {review.overallRating}
              </p>
            )}
            <div className="d-flex justify-content-between align-items-center mt-2">
              <small>
                Conducted by: {review.managerName || `Manager #${review.managerId}`}
              </small>
              <small>
                Last Updated: {new Date(review.updatedAt).toLocaleDateString()}
              </small>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PerformanceReviewList; 