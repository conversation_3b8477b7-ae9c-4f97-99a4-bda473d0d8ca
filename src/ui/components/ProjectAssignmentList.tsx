import React, { useState, useEffect } from 'react';
import { ProjectAssignment } from '../../types/Project';
import { ProjectService } from '../../services/ProjectService';

interface ProjectAssignmentListProps {
  projectId?: number;
  employeeId?: number;
}

const ProjectAssignmentList: React.FC<ProjectAssignmentListProps> = ({ projectId, employeeId }) => {
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        setLoading(true);
        const projectService = new ProjectService();
        
        let assignmentData: ProjectAssignment[] = [];
        
        if (projectId) {
          assignmentData = await projectService.getProjectAssignments(projectId);
        } else if (employeeId) {
          assignmentData = await projectService.getEmployeeProjectAssignments(employeeId);
        } else {
          throw new Error('Either projectId or employeeId must be provided');
        }
        
        setAssignments(assignmentData);
        setError(null);
      } catch (err) {
        console.error('Error fetching project assignments:', err);
        setError('Failed to load project assignments. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [projectId, employeeId]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'active':
        return 'badge bg-success';
      case 'completed':
        return 'badge bg-primary';
      case 'on-hold':
        return 'badge bg-warning';
      default:
        return 'badge bg-secondary';
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading project assignments...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (assignments.length === 0) {
    return <div className="alert alert-info">No project assignments found.</div>;
  }

  return (
    <div className="project-assignment-list">
      <h3>Project Assignments</h3>
      <div className="list-group">
        {assignments.map((assignment) => (
          <div key={assignment.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">
                {projectId 
                  ? `${assignment.employeeName || `Employee #${assignment.employeeId}`}` 
                  : `${assignment.projectName || `Project #${assignment.projectId}`}`}
              </h5>
              <span className={getStatusBadgeClass(assignment.status)}>{assignment.status}</span>
            </div>
            {assignment.role && <p className="mb-1"><strong>Role:</strong> {assignment.role}</p>}
            {assignment.responsibilities && (
              <p className="mb-1"><strong>Responsibilities:</strong> {assignment.responsibilities}</p>
            )}
            <div className="d-flex justify-content-between align-items-center mt-2">
              <div>
                <span className="badge bg-info me-2">{assignment.timeCommitment}% Time Commitment</span>
                {(assignment.startDate || assignment.endDate) && (
                  <span>
                    {assignment.startDate && (
                      <small className="me-2">
                        Start: {new Date(assignment.startDate).toLocaleDateString()}
                      </small>
                    )}
                    {assignment.endDate && (
                      <small>
                        End: {new Date(assignment.endDate).toLocaleDateString()}
                      </small>
                    )}
                  </span>
                )}
              </div>
              <small>
                Last Updated: {new Date(assignment.updatedAt).toLocaleDateString()}
              </small>
            </div>
            {assignment.performanceNotes && (
              <div className="mt-2 p-2 bg-light rounded">
                <small><strong>Performance Notes:</strong> {assignment.performanceNotes}</small>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectAssignmentList; 