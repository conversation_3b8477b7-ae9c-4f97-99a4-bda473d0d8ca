import React, { useState, useEffect } from 'react';
import { Project } from '../../types/Project';
import { ProjectService } from '../../services/ProjectService';

interface ProjectListProps {
  employeeId?: number;
}

const ProjectList: React.FC<ProjectListProps> = ({ employeeId }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const projectService = new ProjectService();
        
        let projectData: Project[] = [];
        
        if (employeeId) {
          // If employeeId is provided, get projects for that employee
          const assignments = await projectService.getEmployeeProjectAssignments(employeeId);
          // Get unique project IDs from assignments
          const projectIds = [...new Set(assignments.map(a => a.projectId))];
          // Fetch each project
          const projectPromises = projectIds.map(id => projectService.getProjectById(id));
          const projects = await Promise.all(projectPromises);
          projectData = projects.filter(p => p !== null) as Project[];
        } else {
          // Otherwise, get all projects
          projectData = await projectService.getAllProjects();
        }
        
        setProjects(projectData);
        setError(null);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Failed to load projects. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [employeeId]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'not-started':
        return 'badge bg-secondary';
      case 'in-progress':
        return 'badge bg-primary';
      case 'completed':
        return 'badge bg-success';
      case 'on-hold':
        return 'badge bg-warning';
      case 'canceled':
        return 'badge bg-danger';
      default:
        return 'badge bg-secondary';
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading projects...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (projects.length === 0) {
    return <div className="alert alert-info">No projects found.</div>;
  }

  return (
    <div className="project-list">
      <h3>Projects</h3>
      <div className="list-group">
        {projects.map((project) => (
          <div key={project.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">{project.name}</h5>
              <span className={getStatusBadgeClass(project.status)}>{project.status}</span>
            </div>
            <p className="mb-1">{project.description}</p>
            {(project.startDate || project.endDate) && (
              <div className="mt-2">
                {project.startDate && (
                  <small className="me-3">
                    Start: {new Date(project.startDate).toLocaleDateString()}
                  </small>
                )}
                {project.endDate && (
                  <small>
                    End: {new Date(project.endDate).toLocaleDateString()}
                  </small>
                )}
              </div>
            )}
            <div className="mt-2">
              <button className="btn btn-sm btn-outline-primary me-2">
                View Details
              </button>
              <button className="btn btn-sm btn-outline-secondary">
                View Assignments
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectList; 