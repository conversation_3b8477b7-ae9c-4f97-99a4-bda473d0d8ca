import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { Goal, CreateGoalData, UpdateGoalData } from '../../types/Goal';
import { GoalService } from '../../services/GoalService';

// Schema for validating goal data
const goalSchema = z.object({
  employeeId: z.number().positive('Employee ID is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  targetDate: z.string().optional(),
  status: z.enum(['not-started', 'in-progress', 'completed', 'on-hold'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
  progress: z.number().min(0).max(100, 'Progress must be between 0 and 100'),
  category: z.enum(['performance', 'development', 'career', 'project'], {
    errorMap: () => ({ message: 'Please select a valid category' }),
  }),
});

interface GoalFormProps {
  goal?: Goal;
  employeeId?: number;
  onSave: (goalId: number) => void;
  onCancel: () => void;
}

const GoalForm: React.FC<GoalFormProps> = ({ goal, employeeId, onSave, onCancel }) => {
  const isEditMode = !!goal;
  
  const [formData, setFormData] = useState<CreateGoalData | UpdateGoalData>({
    employeeId: goal?.employeeId || employeeId || 0,
    title: goal?.title || '',
    description: goal?.description || '',
    targetDate: goal?.targetDate || '',
    status: goal?.status || 'not-started',
    progress: goal?.progress || 0,
    category: goal?.category || 'development',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Reset form when goal or employeeId changes
  useEffect(() => {
    setFormData({
      employeeId: goal?.employeeId || employeeId || 0,
      title: goal?.title || '',
      description: goal?.description || '',
      targetDate: goal?.targetDate || '',
      status: goal?.status || 'not-started',
      progress: goal?.progress || 0,
      category: goal?.category || 'development',
    });
    setErrors({});
    setSubmitError(null);
  }, [goal, employeeId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle number inputs
    if (name === 'progress' || name === 'employeeId') {
      setFormData({
        ...formData,
        [name]: name === 'progress' ? parseInt(value) || 0 : parseInt(value) || undefined,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateForm = (): boolean => {
    try {
      // For edit mode, we need to validate only the fields that are present
      if (isEditMode) {
        const partialSchema = goalSchema.partial();
        partialSchema.parse(formData);
      } else {
        goalSchema.parse(formData);
      }
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      const goalService = new GoalService();
      
      let goalId: number;
      
      if (isEditMode && goal) {
        // Update existing goal
        await goalService.updateGoal(goal.id, formData as UpdateGoalData);
        goalId = goal.id;
      } else {
        // Create new goal
        goalId = await goalService.createGoal(formData as CreateGoalData);
      }
      
      onSave(goalId);
    } catch (error) {
      console.error('Error saving goal:', error);
      setSubmitError('Failed to save goal. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="goal-form">
      <h3>{isEditMode ? 'Edit Goal' : 'Create New Goal'}</h3>
      
      {submitError && (
        <div className="alert alert-danger">{submitError}</div>
      )}
      
      <form onSubmit={handleSubmit}>
        {!isEditMode && (
          <div className="mb-3">
            <label htmlFor="employeeId" className="form-label">Employee ID</label>
            <input
              type="number"
              className={`form-control ${errors.employeeId ? 'is-invalid' : ''}`}
              id="employeeId"
              name="employeeId"
              value={formData.employeeId || ''}
              onChange={handleChange}
              disabled={isEditMode || !!employeeId}
              required
            />
            {errors.employeeId && (
              <div className="invalid-feedback">{errors.employeeId}</div>
            )}
          </div>
        )}
        
        <div className="mb-3">
          <label htmlFor="title" className="form-label">Title</label>
          <input
            type="text"
            className={`form-control ${errors.title ? 'is-invalid' : ''}`}
            id="title"
            name="title"
            value={formData.title || ''}
            onChange={handleChange}
            required
          />
          {errors.title && (
            <div className="invalid-feedback">{errors.title}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="description" className="form-label">Description</label>
          <textarea
            className={`form-control ${errors.description ? 'is-invalid' : ''}`}
            id="description"
            name="description"
            value={formData.description || ''}
            onChange={handleChange}
            rows={3}
          />
          {errors.description && (
            <div className="invalid-feedback">{errors.description}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="targetDate" className="form-label">Target Date</label>
          <input
            type="date"
            className={`form-control ${errors.targetDate ? 'is-invalid' : ''}`}
            id="targetDate"
            name="targetDate"
            value={formData.targetDate || ''}
            onChange={handleChange}
          />
          {errors.targetDate && (
            <div className="invalid-feedback">{errors.targetDate}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="status" className="form-label">Status</label>
          <select
            className={`form-select ${errors.status ? 'is-invalid' : ''}`}
            id="status"
            name="status"
            value={formData.status || 'not-started'}
            onChange={handleChange}
            required
          >
            <option value="not-started">Not Started</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="on-hold">On Hold</option>
          </select>
          {errors.status && (
            <div className="invalid-feedback">{errors.status}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="progress" className="form-label">Progress ({formData.progress}%)</label>
          <input
            type="range"
            className={`form-range ${errors.progress ? 'is-invalid' : ''}`}
            id="progress"
            name="progress"
            min="0"
            max="100"
            value={formData.progress || 0}
            onChange={handleChange}
          />
          {errors.progress && (
            <div className="invalid-feedback">{errors.progress}</div>
          )}
        </div>
        
        <div className="mb-3">
          <label htmlFor="category" className="form-label">Category</label>
          <select
            className={`form-select ${errors.category ? 'is-invalid' : ''}`}
            id="category"
            name="category"
            value={formData.category || 'development'}
            onChange={handleChange}
            required
          >
            <option value="performance">Performance</option>
            <option value="development">Development</option>
            <option value="career">Career</option>
            <option value="project">Project</option>
          </select>
          {errors.category && (
            <div className="invalid-feedback">{errors.category}</div>
          )}
        </div>
        
        <div className="d-flex justify-content-end gap-2">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : isEditMode ? 'Update Goal' : 'Create Goal'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default GoalForm; 