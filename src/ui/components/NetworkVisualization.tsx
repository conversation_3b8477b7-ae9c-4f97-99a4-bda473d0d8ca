import React, { useState, useEffect, useRef } from 'react';
import { EmployeeNetworkInsights, PeerRelationship, EnhancedUser, NetworkGoal } from '../../types/NetworkBasedTypes';

interface NetworkVisualizationProps {
  employeeId: number;
  insights?: EmployeeNetworkInsights;
  relationships?: PeerRelationship[];
  goals?: NetworkGoal[];
  onNodeClick?: (nodeId: number, nodeType: 'employee' | 'goal' | 'skill') => void;
}

interface NetworkNode {
  id: string;
  type: 'employee' | 'goal' | 'skill';
  label: string;
  x: number;
  y: number;
  size: number;
  color: string;
  data: any;
}

interface NetworkEdge {
  from: string;
  to: string;
  type: 'collaboration' | 'mentorship' | 'goal-support' | 'skill-development';
  strength: number;
  color: string;
  width: number;
}

export const NetworkVisualization: React.FC<NetworkVisualizationProps> = ({
  employeeId,
  insights,
  relationships = [],
  goals = [],
  onNodeClick
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [nodes, setNodes] = useState<NetworkNode[]>([]);
  const [edges, setEdges] = useState<NetworkEdge[]>([]);
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [hoveredNode, setHoveredNode] = useState<NetworkNode | null>(null);

  // Initialize network data
  useEffect(() => {
    if (!insights) return;

    const newNodes: NetworkNode[] = [];
    const newEdges: NetworkEdge[] = [];

    // Central employee node
    newNodes.push({
      id: `employee-${employeeId}`,
      type: 'employee',
      label: 'You',
      x: 400,
      y: 300,
      size: 20,
      color: '#3b82f6',
      data: { id: employeeId }
    });

    // Add peer nodes
    relationships.forEach((rel, index) => {
      const angle = (index / relationships.length) * 2 * Math.PI;
      const radius = 150;
      const x = 400 + Math.cos(angle) * radius;
      const y = 300 + Math.sin(angle) * radius;

      newNodes.push({
        id: `employee-${rel.peerId}`,
        type: 'employee',
        label: rel.peer?.firstName || `Peer ${rel.peerId}`,
        x,
        y,
        size: 12 + (rel.relationshipQuality || 5),
        color: getRelationshipColor(rel.relationshipType),
        data: rel.peer
      });

      // Add relationship edge
      newEdges.push({
        from: `employee-${employeeId}`,
        to: `employee-${rel.peerId}`,
        type: 'collaboration',
        strength: rel.relationshipQuality || 5,
        color: getRelationshipColor(rel.relationshipType),
        width: Math.max(1, (rel.relationshipQuality || 5) / 2)
      });
    });

    // Add goal nodes
    goals.forEach((goal, index) => {
      const angle = (index / goals.length) * 2 * Math.PI + Math.PI;
      const radius = 100;
      const x = 400 + Math.cos(angle) * radius;
      const y = 300 + Math.sin(angle) * radius;

      newNodes.push({
        id: `goal-${goal.id}`,
        type: 'goal',
        label: goal.title.substring(0, 20) + '...',
        x,
        y,
        size: 8 + (goal.progress / 10),
        color: getGoalColor(goal.status),
        data: goal
      });

      // Add goal connection edge
      newEdges.push({
        from: `employee-${employeeId}`,
        to: `goal-${goal.id}`,
        type: 'goal-support',
        strength: goal.progress,
        color: '#10b981',
        width: 2
      });

      // Add supporting peer connections
      goal.supportingPeers.forEach(peerId => {
        const peerNodeId = `employee-${peerId}`;
        if (newNodes.find(n => n.id === peerNodeId)) {
          newEdges.push({
            from: peerNodeId,
            to: `goal-${goal.id}`,
            type: 'goal-support',
            strength: 5,
            color: '#f59e0b',
            width: 1
          });
        }
      });
    });

    setNodes(newNodes);
    setEdges(newEdges);
  }, [employeeId, insights, relationships, goals]);

  // Canvas drawing
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw edges
    edges.forEach(edge => {
      const fromNode = nodes.find(n => n.id === edge.from);
      const toNode = nodes.find(n => n.id === edge.to);
      
      if (fromNode && toNode) {
        ctx.beginPath();
        ctx.moveTo(fromNode.x, fromNode.y);
        ctx.lineTo(toNode.x, toNode.y);
        ctx.strokeStyle = edge.color;
        ctx.lineWidth = edge.width;
        ctx.globalAlpha = 0.6;
        ctx.stroke();
        ctx.globalAlpha = 1;
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.size, 0, 2 * Math.PI);
      ctx.fillStyle = node.color;
      
      // Highlight hovered or selected node
      if (hoveredNode?.id === node.id || selectedNode?.id === node.id) {
        ctx.shadowColor = node.color;
        ctx.shadowBlur = 10;
      } else {
        ctx.shadowBlur = 0;
      }
      
      ctx.fill();
      ctx.shadowBlur = 0;

      // Draw node label
      ctx.fillStyle = '#374151';
      ctx.font = '12px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(node.label, node.x, node.y + node.size + 15);
    });
  }, [nodes, edges, hoveredNode, selectedNode]);

  // Handle mouse interactions
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked node
    const clickedNode = nodes.find(node => {
      const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
      return distance <= node.size;
    });

    if (clickedNode) {
      setSelectedNode(clickedNode);
      onNodeClick?.(parseInt(clickedNode.id.split('-')[1]), clickedNode.type);
    } else {
      setSelectedNode(null);
    }
  };

  const handleCanvasMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find hovered node
    const hoveredNode = nodes.find(node => {
      const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
      return distance <= node.size;
    });

    setHoveredNode(hoveredNode || null);
    canvas.style.cursor = hoveredNode ? 'pointer' : 'default';
  };

  return (
    <div className="network-visualization">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Network Visualization</h3>
        <div className="flex gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>You</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Collaborators</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span>Mentors</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span>Goals</span>
          </div>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border border-gray-300 rounded-lg"
        onClick={handleCanvasClick}
        onMouseMove={handleCanvasMouseMove}
      />

      {selectedNode && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold">{selectedNode.label}</h4>
          <p className="text-sm text-gray-600">Type: {selectedNode.type}</p>
          {selectedNode.type === 'employee' && selectedNode.data && (
            <div className="mt-2">
              <p>Role: {selectedNode.data.role}</p>
              <p>Department: {selectedNode.data.department}</p>
            </div>
          )}
          {selectedNode.type === 'goal' && selectedNode.data && (
            <div className="mt-2">
              <p>Progress: {selectedNode.data.progress}%</p>
              <p>Status: {selectedNode.data.status}</p>
              <p>Type: {selectedNode.data.goalType}</p>
            </div>
          )}
        </div>
      )}

      {insights && (
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {(insights.networkStrength * 10).toFixed(1)}
            </div>
            <div className="text-sm text-blue-800">Network Strength</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {(insights.goalCompletionRate * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-green-800">Goal Completion</div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {insights.skillDevelopmentVelocity.toFixed(1)}
            </div>
            <div className="text-sm text-purple-800">Skills/Quarter</div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {(insights.careerProgressionProbability * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-orange-800">Career Progress</div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions
function getRelationshipColor(type: string): string {
  switch (type) {
    case 'mentor': return '#8b5cf6';
    case 'mentee': return '#06b6d4';
    case 'collaborator': return '#10b981';
    case 'knowledge-sharer': return '#f59e0b';
    case 'project-partner': return '#ef4444';
    default: return '#6b7280';
  }
}

function getGoalColor(status: string): string {
  switch (status) {
    case 'completed': return '#10b981';
    case 'in-progress': return '#3b82f6';
    case 'not-started': return '#6b7280';
    case 'on-hold': return '#f59e0b';
    case 'cancelled': return '#ef4444';
    default: return '#6b7280';
  }
}
