import React, { useState, useEffect } from 'react';
import { Goal } from '../../types/Goal';
import { GoalService } from '../../services/GoalService';

interface GoalListProps {
  employeeId: number;
}

const GoalList: React.FC<GoalListProps> = ({ employeeId }) => {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGoals = async () => {
      try {
        setLoading(true);
        const goalService = new GoalService();
        const employeeGoals = await goalService.getEmployeeGoals(employeeId);
        setGoals(employeeGoals);
        setError(null);
      } catch (err) {
        console.error('Error fetching goals:', err);
        setError('Failed to load goals. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchGoals();
  }, [employeeId]);

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'not-started':
        return 'badge bg-secondary';
      case 'in-progress':
        return 'badge bg-primary';
      case 'completed':
        return 'badge bg-success';
      case 'on-hold':
        return 'badge bg-warning';
      default:
        return 'badge bg-secondary';
    }
  };

  if (loading) {
    return <div className="text-center p-4">Loading goals...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (goals.length === 0) {
    return <div className="alert alert-info">No goals found for this employee.</div>;
  }

  return (
    <div className="goal-list">
      <h3>Employee Goals</h3>
      <div className="list-group">
        {goals.map((goal) => (
          <div key={goal.id} className="list-group-item">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-1">{goal.title}</h5>
              <span className={getStatusBadgeClass(goal.status)}>{goal.status}</span>
            </div>
            <p className="mb-1">{goal.description}</p>
            <div className="d-flex justify-content-between align-items-center">
              <small>Category: {goal.category}</small>
              <small>Target Date: {goal.targetDate ? new Date(goal.targetDate).toLocaleDateString() : 'Not set'}</small>
            </div>
            <div className="progress mt-2">
              <div 
                className="progress-bar" 
                role="progressbar" 
                style={{ width: `${goal.progress}%` }} 
                aria-valuenow={goal.progress} 
                aria-valuemin={0} 
                aria-valuemax={100}
              >
                {goal.progress}%
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GoalList; 