import '@testing-library/jest-dom';

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret';
process.env.DATABASE_URL = 'postgres://postgres:postgres@localhost:5432/confidentcoaching_test';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock fetch for API tests
global.fetch = jest.fn();

// Setup test database connection mock
jest.mock('./config/database', () => ({
  connectDatabase: jest.fn().mockResolvedValue({}),
  query: jest.fn(),
  getClient: jest.fn(),
  closeDatabase: jest.fn(),
}));
