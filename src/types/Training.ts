/**
 * Represents a training record for an employee
 */
export interface Training {
  id: number;
  employeeId: number;
  employeeName?: string;
  title: string;
  description?: string;
  type: 'course' | 'workshop' | 'conference' | 'certification' | 'other';
  provider?: string;
  startDate?: string;
  endDate?: string;
  status: 'planned' | 'in-progress' | 'completed' | 'canceled';
  completionCertificate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Data required to create a new training record
 */
export interface CreateTrainingData {
  employeeId: number;
  title: string;
  description?: string;
  type: 'course' | 'workshop' | 'conference' | 'certification' | 'other';
  provider?: string;
  startDate?: string;
  endDate?: string;
  status: 'planned' | 'in-progress' | 'completed' | 'canceled';
  completionCertificate?: string;
  notes?: string;
}

/**
 * Data for updating an existing training record
 */
export interface UpdateTrainingData {
  title?: string;
  description?: string;
  type?: 'course' | 'workshop' | 'conference' | 'certification' | 'other';
  provider?: string;
  startDate?: string;
  endDate?: string;
  status?: 'planned' | 'in-progress' | 'completed' | 'canceled';
  completionCertificate?: string;
  notes?: string;
} 