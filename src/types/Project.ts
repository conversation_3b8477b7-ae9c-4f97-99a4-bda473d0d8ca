/**
 * Represents a project
 */
export interface Project {
  id: number;
  name: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold' | 'canceled';
  createdAt: string;
  updatedAt: string;
}

/**
 * Data required to create a new project
 */
export interface CreateProjectData {
  name: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold' | 'canceled';
}

/**
 * Data for updating an existing project
 */
export interface UpdateProjectData {
  name?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  status?: 'not-started' | 'in-progress' | 'completed' | 'on-hold' | 'canceled';
}

/**
 * Represents a project assignment for an employee
 */
export interface ProjectAssignment {
  id: number;
  projectId: number;
  projectName?: string;
  employeeId: number;
  employeeName?: string;
  role?: string;
  responsibilities?: string;
  startDate?: string;
  endDate?: string;
  timeCommitment: number;
  status: 'active' | 'completed' | 'on-hold';
  performanceNotes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Data required to create a new project assignment
 */
export interface CreateProjectAssignmentData {
  projectId: number;
  employeeId: number;
  role?: string;
  responsibilities?: string;
  startDate?: string;
  endDate?: string;
  timeCommitment: number;
  status: 'active' | 'completed' | 'on-hold';
  performanceNotes?: string;
}

/**
 * Data for updating an existing project assignment
 */
export interface UpdateProjectAssignmentData {
  role?: string;
  responsibilities?: string;
  startDate?: string;
  endDate?: string;
  timeCommitment?: number;
  status?: 'active' | 'completed' | 'on-hold';
  performanceNotes?: string;
} 