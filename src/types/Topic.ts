/**
 * Represents a discussion topic
 */
export interface Topic {
  id: number;
  title: string;
  description?: string;
  category: 'technical' | 'soft-skills' | 'project' | 'career' | 'performance' | 'other';
  status: 'active' | 'completed' | 'archived';
  createdBy: number;
  creatorName?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Data required to create a new topic
 */
export interface CreateTopicData {
  title: string;
  description?: string;
  category: 'technical' | 'soft-skills' | 'project' | 'career' | 'performance' | 'other';
  status: 'active' | 'completed' | 'archived';
  createdBy: number;
}

/**
 * Data for updating an existing topic
 */
export interface UpdateTopicData {
  title?: string;
  description?: string;
  category?: 'technical' | 'soft-skills' | 'project' | 'career' | 'performance' | 'other';
  status?: 'active' | 'completed' | 'archived';
} 