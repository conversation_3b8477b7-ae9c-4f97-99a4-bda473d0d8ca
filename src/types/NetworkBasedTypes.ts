/**
 * Enhanced types for network-based line management
 * Based on Team Topologies and From Bud to Boss principles
 */

// ============================================================================
// CORE ENTITIES
// ============================================================================

export interface EnhancedUser {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
  role: string;
  employeeId?: string;
  department?: string;
  jobTitle?: string;
  hireDate?: string;
  managerId?: number;
  teamType: 'stream-aligned' | 'platform' | 'enabling' | 'complicated-subsystem';
  cognitiveLoadLevel: number; // 1-10 scale
  preferredCommunicationStyle: 'directive' | 'collaborative' | 'supportive' | 'delegating';
  careerStage: 'new' | 'developing' | 'proficient' | 'expert' | 'master';
  createdAt: string;
  updatedAt: string;
}

export interface Skill {
  id: number;
  name: string;
  category: string; // technical, leadership, communication, domain-specific
  skillType: 'hard' | 'soft' | 'hybrid';
  description?: string;
  proficiencyLevels: string[];
  isCoreSkill: boolean;
  createdAt: string;
}

export interface EmployeeSkill {
  id: number;
  employeeId: number;
  skillId: number;
  currentLevel: string;
  targetLevel?: string;
  lastAssessedDate?: string;
  assessedBy?: number;
  evidenceNotes?: string;
  developmentPriority: 'low' | 'medium' | 'high' | 'critical';
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  skill?: Skill;
  assessor?: EnhancedUser;
}

export interface CareerAspiration {
  id: number;
  employeeId: number;
  aspirationType: 'role' | 'skill' | 'responsibility' | 'location' | 'team';
  targetRole?: string;
  targetDepartment?: string;
  targetTimeline: '6-months' | '1-year' | '2-years' | '5-years';
  motivation?: string;
  currentGapAnalysis?: string;
  developmentActions?: string;
  probabilityScore: number; // 1-10 scale
  status: 'active' | 'achieved' | 'modified' | 'abandoned';
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// INTERACTION AND RELATIONSHIP TRACKING
// ============================================================================

export interface ManagerInteraction {
  id: number;
  managerId: number;
  employeeId: number;
  interactionType: 'one-on-one' | 'coaching' | 'feedback' | 'check-in' | 'career-discussion';
  interactionDate: string;
  durationMinutes?: number;
  location?: string;
  
  // Structured interaction data
  topicsDiscussed: string[];
  employeeMood?: 'energized' | 'motivated' | 'concerned' | 'frustrated' | 'confused';
  engagementLevel?: number; // 1-10 scale
  
  // Key conversation elements
  winsCelebrated?: string;
  challengesDiscussed?: string;
  supportRequested?: string;
  actionsAgreed: InteractionAction[];
  
  // Manager observations
  managerNotes?: string;
  developmentOpportunitiesIdentified?: string;
  followUpRequired: boolean;
  followUpDate?: string;
  
  // Relationship building
  personalUpdates?: string;
  trustBuildingNotes?: string;
  
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  manager?: EnhancedUser;
  employee?: EnhancedUser;
}

export interface InteractionAction {
  action: string;
  owner: 'manager' | 'employee' | 'both';
  dueDate?: string;
  status?: 'pending' | 'in-progress' | 'completed' | 'overdue';
}

export interface PeerRelationship {
  id: number;
  employeeId: number;
  peerId: number;
  relationshipType: 'mentor' | 'mentee' | 'collaborator' | 'knowledge-sharer' | 'project-partner';
  relationshipStrength: 'weak' | 'moderate' | 'strong';
  collaborationFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'occasional';
  knowledgeFlowDirection: 'to-peer' | 'from-peer' | 'bidirectional';
  
  // Relationship context
  context?: string;
  sharedProjects: number[];
  sharedSkills: number[];
  
  // Relationship health
  lastInteractionDate?: string;
  relationshipQuality?: number; // 1-10 scale
  
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  employee?: EnhancedUser;
  peer?: EnhancedUser;
}

// ============================================================================
// ENHANCED GOALS WITH NETWORK CONNECTIONS
// ============================================================================

export interface NetworkGoal {
  id: number;
  employeeId: number;
  title: string;
  description?: string;
  
  // Goal classification
  goalType: 'performance' | 'development' | 'career' | 'project' | 'team';
  category: 'individual' | 'team' | 'organizational';
  
  // Timeline and progress
  targetDate?: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'on-hold' | 'cancelled';
  progress: number; // 0-100
  
  // Network connections
  relatedSkills: number[];
  supportingPeers: number[];
  dependentGoals: number[];
  
  // Success criteria and measurement
  successCriteria: SuccessCriterion[];
  measurementMethod?: string;
  
  // Support and resources
  resourcesNeeded?: string;
  managerSupportRequired?: string;
  
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  employee?: EnhancedUser;
  skills?: Skill[];
  supporters?: EnhancedUser[];
  dependencies?: GoalDependency[];
}

export interface SuccessCriterion {
  criterion: string;
  measurementType: 'quantitative' | 'qualitative' | 'binary';
  targetValue?: string;
  currentValue?: string;
  achieved: boolean;
}

export interface GoalDependency {
  id: number;
  goalId: number;
  dependsOnGoalId: number;
  dependencyType: 'prerequisite' | 'supporting' | 'blocking' | 'related';
  dependencyStrength: 'weak' | 'medium' | 'strong';
  notes?: string;
  createdAt: string;
  
  // Populated fields
  dependentGoal?: NetworkGoal;
}

// ============================================================================
// TEAM TOPOLOGY AND ORGANIZATIONAL STRUCTURE
// ============================================================================

export interface Team {
  id: number;
  name: string;
  teamType: 'stream-aligned' | 'platform' | 'enabling' | 'complicated-subsystem';
  purpose?: string;
  domainExpertise: string[];
  cognitiveLoadAssessment?: number; // 1-10 scale
  
  // Team health metrics
  autonomyLevel?: number; // 1-10 scale
  masteryLevel?: number; // 1-10 scale
  purposeClarity?: number; // 1-10 scale
  
  // Interaction patterns
  preferredInteractionModes: ('collaboration' | 'x-as-a-service' | 'facilitating')[];
  
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  members?: TeamMembership[];
}

export interface TeamMembership {
  id: number;
  teamId: number;
  employeeId: number;
  roleInTeam?: string;
  startDate: string;
  endDate?: string;
  contributionLevel: 'full-time' | 'part-time' | 'consultant' | 'advisor';
  createdAt: string;
  
  // Populated fields
  team?: Team;
  employee?: EnhancedUser;
}

// ============================================================================
// ANALYTICS AND INSIGHTS
// ============================================================================

export interface EmployeeNetworkInsights {
  employeeId: number;
  
  // Skill development insights
  skillGaps: SkillGap[];
  skillDevelopmentVelocity: number; // Skills improved per quarter
  
  // Relationship insights
  networkStrength: number; // Overall relationship quality score
  mentorshipBalance: number; // Ratio of mentoring given vs received
  collaborationDiversity: number; // How diverse their collaboration network is
  
  // Goal achievement insights
  goalCompletionRate: number; // Percentage of goals completed on time
  goalNetworkEffectiveness: number; // How well they leverage their network for goals
  
  // Career development insights
  careerProgressionProbability: number; // Likelihood of achieving career aspirations
  developmentOpportunityAlignment: number; // How well current activities align with aspirations
  
  // Team contribution insights
  teamImpact: number; // Impact on team performance
  knowledgeSharingScore: number; // How much they contribute to team knowledge
  
  lastCalculated: string;
}

export interface SkillGap {
  skillId: number;
  skill: Skill;
  currentLevel: string;
  targetLevel: string;
  gapSize: number; // Levels between current and target
  developmentPriority: 'low' | 'medium' | 'high' | 'critical';
  suggestedActions: string[];
}

// ============================================================================
// INTERACTION FLOW TYPES
// ============================================================================

export interface InteractionFlow {
  id: string;
  flowType: 'one-on-one-cycle' | 'goal-review-cycle' | 'career-development-cycle' | 'feedback-cycle';
  participantIds: number[];
  currentStage: string;
  nextStageDate?: string;
  flowData: Record<string, any>;
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
}

export interface OneOnOneFlow extends InteractionFlow {
  flowType: 'one-on-one-cycle';
  flowData: {
    frequency: 'weekly' | 'bi-weekly' | 'monthly';
    lastMeeting?: string;
    nextMeeting?: string;
    agendaTemplate: string[];
    recurringTopics: string[];
    employeePreferences: {
      preferredDay?: string;
      preferredTime?: string;
      preferredLocation?: string;
      preferredDuration?: number;
    };
  };
}
