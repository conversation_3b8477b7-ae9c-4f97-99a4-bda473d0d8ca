/**
 * Represents a performance review for an employee
 */
export interface PerformanceReview {
  id: number;
  employeeId: number;
  employeeName?: string;
  managerId: number;
  managerName?: string;
  reviewDate: string;
  reviewPeriodStart?: string;
  reviewPeriodEnd?: string;
  overallRating?: string;
  strengths?: string;
  areasForImprovement?: string;
  comments?: string;
  employeeComments?: string;
  status: 'draft' | 'in-review' | 'completed';
  createdAt: string;
  updatedAt: string;
}

/**
 * Data required to create a new performance review
 */
export interface CreatePerformanceReviewData {
  employeeId: number;
  managerId: number;
  reviewDate: string;
  reviewPeriodStart?: string;
  reviewPeriodEnd?: string;
  overallRating?: string;
  strengths?: string;
  areasForImprovement?: string;
  comments?: string;
  employeeComments?: string;
  status: 'draft' | 'in-review' | 'completed';
}

/**
 * Data for updating an existing performance review
 */
export interface UpdatePerformanceReviewData {
  reviewDate?: string;
  reviewPeriodStart?: string;
  reviewPeriodEnd?: string;
  overallRating?: string;
  strengths?: string;
  areasForImprovement?: string;
  comments?: string;
  employeeComments?: string;
  status?: 'draft' | 'in-review' | 'completed';
} 