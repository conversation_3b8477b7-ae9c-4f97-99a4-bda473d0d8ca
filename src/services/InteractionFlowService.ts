/**
 * Interaction Flow Service
 * Manages the network-based interaction flows between line managers and employees
 * Based on Team Topologies and From Bud to Boss principles
 */

import { query } from '../config/database';
import { logger } from '../config/logger';
import { 
  ManagerInteraction, 
  InteractionAction, 
  NetworkGoal, 
  EmployeeSkill, 
  CareerAspiration,
  PeerRelationship,
  EmployeeNetworkInsights,
  OneOnOneFlow
} from '../types/NetworkBasedTypes';

export class InteractionFlowService {
  
  /**
   * Create a structured manager-employee interaction
   * Captures the full context of the conversation
   */
  async createManagerInteraction(interaction: Omit<ManagerInteraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<number> {
    try {
      const result = await query(`
        INSERT INTO manager_interactions (
          manager_id, employee_id, interaction_type, interaction_date, duration_minutes, location,
          topics_discussed, employee_mood, engagement_level, wins_celebrated, challenges_discussed,
          support_requested, actions_agreed, manager_notes, development_opportunities_identified,
          follow_up_required, follow_up_date, personal_updates, trust_building_notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
        RETURNING id
      `, [
        interaction.managerId,
        interaction.employeeId,
        interaction.interactionType,
        interaction.interactionDate,
        interaction.durationMinutes,
        interaction.location,
        interaction.topicsDiscussed,
        interaction.employeeMood,
        interaction.engagementLevel,
        interaction.winsCelebrated,
        interaction.challengesDiscussed,
        interaction.supportRequested,
        JSON.stringify(interaction.actionsAgreed),
        interaction.managerNotes,
        interaction.developmentOpportunitiesIdentified,
        interaction.followUpRequired,
        interaction.followUpDate,
        interaction.personalUpdates,
        interaction.trustBuildingNotes
      ]);

      const interactionId = result.rows[0].id;
      
      // Auto-create follow-up goals if development opportunities were identified
      if (interaction.developmentOpportunitiesIdentified) {
        await this.createDevelopmentGoalsFromInteraction(interactionId, interaction);
      }

      logger.info(`Created manager interaction: ${interactionId}`, {
        managerId: interaction.managerId,
        employeeId: interaction.employeeId,
        type: interaction.interactionType
      });

      return interactionId;
    } catch (error) {
      logger.error('Error creating manager interaction:', error);
      throw error;
    }
  }

  /**
   * Get interaction history with network insights
   */
  async getInteractionHistory(managerId: number, employeeId: number, limit: number = 10): Promise<ManagerInteraction[]> {
    try {
      const result = await query(`
        SELECT 
          mi.*,
          m.first_name as manager_first_name,
          m.last_name as manager_last_name,
          e.first_name as employee_first_name,
          e.last_name as employee_last_name
        FROM manager_interactions mi
        JOIN users m ON mi.manager_id = m.id
        JOIN users e ON mi.employee_id = e.id
        WHERE mi.manager_id = $1 AND mi.employee_id = $2
        ORDER BY mi.interaction_date DESC
        LIMIT $3
      `, [managerId, employeeId, limit]);

      return result.rows.map(row => ({
        id: row.id,
        managerId: row.manager_id,
        employeeId: row.employee_id,
        interactionType: row.interaction_type,
        interactionDate: row.interaction_date,
        durationMinutes: row.duration_minutes,
        location: row.location,
        topicsDiscussed: row.topics_discussed || [],
        employeeMood: row.employee_mood,
        engagementLevel: row.engagement_level,
        winsCelebrated: row.wins_celebrated,
        challengesDiscussed: row.challenges_discussed,
        supportRequested: row.support_requested,
        actionsAgreed: row.actions_agreed ? JSON.parse(row.actions_agreed) : [],
        managerNotes: row.manager_notes,
        developmentOpportunitiesIdentified: row.development_opportunities_identified,
        followUpRequired: row.follow_up_required,
        followUpDate: row.follow_up_date,
        personalUpdates: row.personal_updates,
        trustBuildingNotes: row.trust_building_notes,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        manager: {
          id: row.manager_id,
          firstName: row.manager_first_name,
          lastName: row.manager_last_name
        },
        employee: {
          id: row.employee_id,
          firstName: row.employee_first_name,
          lastName: row.employee_last_name
        }
      }));
    } catch (error) {
      logger.error('Error getting interaction history:', error);
      throw error;
    }
  }

  /**
   * Create development goals from interaction insights
   */
  private async createDevelopmentGoalsFromInteraction(interactionId: number, interaction: Omit<ManagerInteraction, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    if (!interaction.developmentOpportunitiesIdentified) return;

    try {
      // Parse development opportunities and create goals
      const opportunities = interaction.developmentOpportunitiesIdentified.split('\n').filter(opp => opp.trim());
      
      for (const opportunity of opportunities) {
        await query(`
          INSERT INTO goals (
            employee_id, title, description, goal_type, category, status,
            resources_needed, manager_support_required
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `, [
          interaction.employeeId,
          `Development: ${opportunity.substring(0, 100)}`,
          `Development opportunity identified in ${interaction.interactionType} on ${interaction.interactionDate}: ${opportunity}`,
          'development',
          'individual',
          'not-started',
          'To be determined in follow-up discussion',
          'Manager support required for planning and resources'
        ]);
      }

      logger.info(`Created development goals from interaction ${interactionId}`);
    } catch (error) {
      logger.error('Error creating development goals from interaction:', error);
    }
  }

  /**
   * Get employee network insights
   */
  async getEmployeeNetworkInsights(employeeId: number): Promise<EmployeeNetworkInsights> {
    try {
      // Get skill development velocity
      const skillVelocity = await this.calculateSkillDevelopmentVelocity(employeeId);
      
      // Get network strength
      const networkStrength = await this.calculateNetworkStrength(employeeId);
      
      // Get goal completion rate
      const goalCompletionRate = await this.calculateGoalCompletionRate(employeeId);
      
      // Get career progression probability
      const careerProgressionProbability = await this.calculateCareerProgressionProbability(employeeId);

      return {
        employeeId,
        skillGaps: await this.identifySkillGaps(employeeId),
        skillDevelopmentVelocity: skillVelocity,
        networkStrength,
        mentorshipBalance: await this.calculateMentorshipBalance(employeeId),
        collaborationDiversity: await this.calculateCollaborationDiversity(employeeId),
        goalCompletionRate,
        goalNetworkEffectiveness: await this.calculateGoalNetworkEffectiveness(employeeId),
        careerProgressionProbability,
        developmentOpportunityAlignment: await this.calculateDevelopmentAlignment(employeeId),
        teamImpact: await this.calculateTeamImpact(employeeId),
        knowledgeSharingScore: await this.calculateKnowledgeSharingScore(employeeId),
        lastCalculated: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error calculating employee network insights:', error);
      throw error;
    }
  }

  /**
   * Suggest next interaction based on employee state and network
   */
  async suggestNextInteraction(managerId: number, employeeId: number): Promise<{
    suggestedType: string;
    suggestedTopics: string[];
    priority: 'low' | 'medium' | 'high' | 'urgent';
    reasoning: string;
  }> {
    try {
      // Get recent interactions
      const recentInteractions = await this.getInteractionHistory(managerId, employeeId, 5);
      
      // Get employee insights
      const insights = await this.getEmployeeNetworkInsights(employeeId);
      
      // Get pending actions
      const pendingActions = await this.getPendingActions(managerId, employeeId);

      // Determine suggestion based on various factors
      let suggestedType = 'check-in';
      let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
      const suggestedTopics: string[] = [];
      let reasoning = '';

      // Check if overdue for regular one-on-one
      const lastOneOnOne = recentInteractions.find(i => i.interactionType === 'one-on-one');
      const daysSinceLastOneOnOne = lastOneOnOne 
        ? Math.floor((Date.now() - new Date(lastOneOnOne.interactionDate).getTime()) / (1000 * 60 * 60 * 24))
        : 999;

      if (daysSinceLastOneOnOne > 14) {
        suggestedType = 'one-on-one';
        priority = daysSinceLastOneOnOne > 30 ? 'urgent' : 'high';
        reasoning = `Overdue for regular one-on-one (${daysSinceLastOneOnOne} days since last)`;
        suggestedTopics.push('General check-in', 'Goal progress review', 'Support needs');
      }

      // Check for pending follow-ups
      if (pendingActions.length > 0) {
        priority = 'high';
        suggestedTopics.push('Follow-up on previous actions');
        reasoning += ` ${pendingActions.length} pending action items need follow-up.`;
      }

      // Check for skill development opportunities
      if (insights.skillGaps.length > 0) {
        suggestedTopics.push('Skill development planning');
        if (insights.skillDevelopmentVelocity < 0.5) {
          priority = 'high';
          reasoning += ' Low skill development velocity detected.';
        }
      }

      // Check for career development needs
      if (insights.careerProgressionProbability < 0.6) {
        suggestedType = 'career-discussion';
        suggestedTopics.push('Career aspiration review', 'Development path planning');
        reasoning += ' Career progression probability is low.';
      }

      // Check for network/relationship issues
      if (insights.networkStrength < 0.5) {
        suggestedTopics.push('Team collaboration', 'Relationship building');
        reasoning += ' Network strength is below optimal.';
      }

      return {
        suggestedType,
        suggestedTopics,
        priority,
        reasoning: reasoning || 'Regular check-in recommended'
      };
    } catch (error) {
      logger.error('Error suggesting next interaction:', error);
      throw error;
    }
  }

  // Helper methods for calculations
  private async calculateSkillDevelopmentVelocity(employeeId: number): Promise<number> {
    // Implementation would calculate skills improved per quarter
    return 1.2; // Placeholder
  }

  private async calculateNetworkStrength(employeeId: number): Promise<number> {
    // Implementation would analyze peer relationships and collaboration patterns
    return 0.75; // Placeholder
  }

  private async calculateGoalCompletionRate(employeeId: number): Promise<number> {
    // Implementation would calculate percentage of goals completed on time
    return 0.85; // Placeholder
  }

  private async calculateCareerProgressionProbability(employeeId: number): Promise<number> {
    // Implementation would analyze career aspirations vs current trajectory
    return 0.7; // Placeholder
  }

  private async identifySkillGaps(employeeId: number): Promise<any[]> {
    // Implementation would identify gaps between current and target skill levels
    return []; // Placeholder
  }

  private async calculateMentorshipBalance(employeeId: number): Promise<number> {
    // Implementation would calculate ratio of mentoring given vs received
    return 0.6; // Placeholder
  }

  private async calculateCollaborationDiversity(employeeId: number): Promise<number> {
    // Implementation would measure diversity of collaboration network
    return 0.8; // Placeholder
  }

  private async calculateGoalNetworkEffectiveness(employeeId: number): Promise<number> {
    // Implementation would measure how well they leverage network for goals
    return 0.7; // Placeholder
  }

  private async calculateDevelopmentAlignment(employeeId: number): Promise<number> {
    // Implementation would measure alignment between activities and aspirations
    return 0.75; // Placeholder
  }

  private async calculateTeamImpact(employeeId: number): Promise<number> {
    // Implementation would measure impact on team performance
    return 0.8; // Placeholder
  }

  private async calculateKnowledgeSharingScore(employeeId: number): Promise<number> {
    // Implementation would measure knowledge sharing contribution
    return 0.65; // Placeholder
  }

  private async getPendingActions(managerId: number, employeeId: number): Promise<InteractionAction[]> {
    // Implementation would get pending actions from recent interactions
    return []; // Placeholder
  }
}
