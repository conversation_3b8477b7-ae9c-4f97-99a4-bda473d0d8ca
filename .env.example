# Application Configuration
NODE_ENV=development
PORT=3001
CLIENT_PORT=3000

# Database Configuration
DATABASE_URL=postgres://postgres:postgres@localhost:5432/confidentcoaching
DB_HOST=localhost
DB_PORT=5432
DB_NAME=confidentcoaching
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Email Configuration (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# File Upload (optional)
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Monitoring (optional)
SENTRY_DSN=
HEALTH_CHECK_INTERVAL=30000
