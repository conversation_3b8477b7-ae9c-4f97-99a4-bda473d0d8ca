{"name": "confident-coaching", "version": "1.0.0", "description": "A comprehensive application for managing employee performance reviews, training, projects, and professional development", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec ts-node src/server.ts", "dev:client": "vite", "build": "npm run build:server && npm run build:client", "build:server": "tsc -p tsconfig.server.json", "build:client": "vite build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "typecheck": "tsc --noEmit", "migrate": "ts-node src/database/migrate.ts", "seed": "ts-node src/database/seed.ts", "docker:build": "docker build -t confident-coaching .", "docker:run": "docker run -p 3000:3000 -p 3001:3001 confident-coaching"}, "keywords": ["performance-management", "employee-development", "typescript", "react", "express", "postgresql"], "author": "ConfidentCoaching Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-pool": "^3.6.1", "zod": "^3.22.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "morgan": "^1.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "axios": "^1.6.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15"}, "devDependencies": {"typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "concurrently": "^8.2.2", "vite": "^4.5.0", "@vitejs/plugin-react": "^4.1.1", "@types/node": "^20.8.10", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.7", "@types/morgan": "^1.9.9", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}