{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "noEmit": false, "jsx": "preserve", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true}, "include": ["src/server.ts", "src/api/**/*", "src/services/**/*", "src/types/**/*", "src/config/**/*", "src/utils/**/*", "src/database/**/*"], "exclude": ["src/ui/**/*", "src/**/*.test.ts", "src/**/*.spec.ts", "node_modules", "dist"]}