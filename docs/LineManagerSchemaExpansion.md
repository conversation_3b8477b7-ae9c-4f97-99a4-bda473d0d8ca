# Line Manager Schema Expansion

## Overview

The Line Manager Schema Expansion feature enhances the database schema to support comprehensive line manager duties beyond just 1:1 meetings. This includes goal setting, performance reviews, training management, topic organization, and project oversight.

## Implemented Entities

### 1. Goal

Goals track employee objectives and progress.

**Key Attributes:**
- Employee assignment
- Title and description
- Target date and progress tracking
- Status (not-started, in-progress, completed, on-hold)
- Category (performance, development, career, project)

**Relationships:**
- Belongs to an Employee
- Can be linked to Performance Reviews
- Can be linked to Topics
- Can be linked to Trainings
- Can have Action Items

### 2. Performance Review

Performance Reviews document formal guidance and evaluations.

**Key Attributes:**
- Employee and manager assignment
- Review date and period
- Overall rating
- Strengths and areas for improvement
- Comments from both manager and employee
- Status (draft, in-review, completed)

**Relationships:**
- Belongs to an Employee
- Conducted by a Manager
- Can include multiple Goals
- Can generate Action Items

### 3. Training

Training records support employee enablement through development tracking.

**Key Attributes:**
- Employee assignment
- Title and description
- Type (course, workshop, conference, certification, other)
- Provider information
- Start and end dates
- Status (planned, in-progress, completed, canceled)
- Completion certificate

**Relationships:**
- Belongs to an Employee
- Can be linked to Goals
- Can have Action Items

### 4. Topic

Topics organize specific focus areas discussed over time.

**Key Attributes:**
- Title and description
- Category (technical, soft-skills, project, career, performance, other)
- Status (active, completed, archived)
- Creator information

**Relationships:**
- Can be discussed in Meetings
- Can have Action Items
- Can be linked to Goals
- Can be linked to Projects

### 5. Project and Project Assignment

Projects track initiatives, while Project Assignments track employee roles in projects.

**Project Key Attributes:**
- Name and description
- Start and end dates
- Status (not-started, in-progress, completed, on-hold, canceled)

**Project Assignment Key Attributes:**
- Project and employee assignment
- Role and responsibilities
- Start and end dates
- Time commitment percentage
- Status (active, completed, on-hold)
- Performance notes

**Relationships:**
- Projects have many Project Assignments
- Project Assignments belong to an Employee
- Project Assignments can have Action Items
- Projects can be linked to Topics

## Implementation Details

### Database Schema

The database schema has been expanded with the following tables:

1. `goals` - Stores employee goals
2. `performance_reviews` - Stores performance review records
3. `trainings` - Stores employee training records
4. `topics` - Stores discussion topics
5. `projects` - Stores project information
6. `project_assignments` - Stores employee project assignments

Junction tables for many-to-many relationships:
1. `performance_review_goals` - Links performance reviews to goals
2. `training_goals` - Links trainings to goals
3. `topic_goals` - Links topics to goals
4. `goal_discussions` - Tracks when goals are discussed in meetings

### Service Layer

The service layer provides a clean API for interacting with the database:

1. `GoalService` - Manages employee goals
2. `PerformanceReviewService` - Manages performance reviews
3. `TrainingService` - Manages employee training records
4. `TopicService` - Manages discussion topics
5. `ProjectService` - Manages projects and project assignments

Each service provides methods for:
- Creating new records
- Retrieving records by ID
- Retrieving collections of records
- Updating records
- Deleting records
- Managing relationships between entities

### Database Access Layer

The `DatabaseManager` class has been extended with methods for each entity:

1. Goal methods:
   - `createGoal`, `getGoalById`, `getEmployeeGoals`, `updateGoal`, `deleteGoal`, `getGoalActionItems`

2. Performance Review methods:
   - `createPerformanceReview`, `getPerformanceReviewById`, `getEmployeePerformanceReviews`, `getManagerPerformanceReviews`, `updatePerformanceReview`, `deletePerformanceReview`, `getPerformanceReviewGoals`, `addGoalToPerformanceReview`, `removeGoalFromPerformanceReview`

3. Training methods:
   - `createTraining`, `getTrainingById`, `getEmployeeTrainings`, `updateTraining`, `deleteTraining`, `getTrainingGoals`, `addGoalToTraining`, `removeGoalFromTraining`

4. Topic methods:
   - `createTopic`, `getTopicById`, `getAllTopics`, `getUserTopics`, `updateTopic`, `deleteTopic`, `getTopicGoals`, `addGoalToTopic`, `removeGoalFromTopic`, `getTopicActionItems`

5. Project methods:
   - `createProject`, `getProjectById`, `getAllProjects`, `updateProject`, `deleteProject`

6. Project Assignment methods:
   - `createProjectAssignment`, `getProjectAssignmentById`, `getProjectAssignments`, `getEmployeeProjectAssignments`, `updateProjectAssignment`, `deleteProjectAssignment`

### API Layer

The API layer exposes the service functionality through RESTful endpoints:

1. Goal API (`/api/goals`):
   - `GET /api/goals/employee/:employeeId` - Get all goals for an employee
   - `GET /api/goals/:id` - Get a goal by ID
   - `POST /api/goals` - Create a new goal
   - `PUT /api/goals/:id` - Update a goal
   - `DELETE /api/goals/:id` - Delete a goal
   - `GET /api/goals/:id/action-items` - Get action items for a goal

2. Performance Review API (`/api/performance-reviews`):
   - `GET /api/performance-reviews/employee/:employeeId` - Get all performance reviews for an employee
   - `GET /api/performance-reviews/manager/:managerId` - Get all performance reviews conducted by a manager
   - `GET /api/performance-reviews/:id` - Get a performance review by ID
   - `POST /api/performance-reviews` - Create a new performance review
   - `PUT /api/performance-reviews/:id` - Update a performance review
   - `DELETE /api/performance-reviews/:id` - Delete a performance review
   - `GET /api/performance-reviews/:id/goals` - Get goals associated with a performance review
   - `POST /api/performance-reviews/:id/goals/:goalId` - Add a goal to a performance review
   - `DELETE /api/performance-reviews/:id/goals/:goalId` - Remove a goal from a performance review

3. Training API (`/api/trainings`):
   - `GET /api/trainings/employee/:employeeId` - Get all training records for an employee
   - `GET /api/trainings/:id` - Get a training record by ID
   - `POST /api/trainings` - Create a new training record
   - `PUT /api/trainings/:id` - Update a training record
   - `DELETE /api/trainings/:id` - Delete a training record
   - `GET /api/trainings/:id/goals` - Get goals associated with a training record
   - `POST /api/trainings/:id/goals/:goalId` - Add a goal to a training record
   - `DELETE /api/trainings/:id/goals/:goalId` - Remove a goal from a training record

4. Topic API (`/api/topics`):
   - `GET /api/topics` - Get all topics
   - `GET /api/topics/user/:userId` - Get all topics created by a user
   - `GET /api/topics/:id` - Get a topic by ID
   - `POST /api/topics` - Create a new topic
   - `PUT /api/topics/:id` - Update a topic
   - `DELETE /api/topics/:id` - Delete a topic
   - `GET /api/topics/:id/goals` - Get goals associated with a topic
   - `POST /api/topics/:id/goals/:goalId` - Add a goal to a topic
   - `DELETE /api/topics/:id/goals/:goalId` - Remove a goal from a topic
   - `GET /api/topics/:id/action-items` - Get action items for a topic

5. Project API (`/api/projects`):
   - `GET /api/projects` - Get all projects
   - `GET /api/projects/:id` - Get a project by ID
   - `POST /api/projects` - Create a new project
   - `PUT /api/projects/:id` - Update a project
   - `DELETE /api/projects/:id` - Delete a project
   - `GET /api/projects/:id/assignments` - Get all assignments for a project
   - `GET /api/projects/employee/:employeeId/assignments` - Get all project assignments for an employee
   - `GET /api/projects/assignments/:id` - Get a project assignment by ID
   - `POST /api/projects/assignments` - Create a new project assignment
   - `PUT /api/projects/assignments/:id` - Update a project assignment
   - `DELETE /api/projects/assignments/:id` - Delete a project assignment

### UI Components

The UI layer includes React components for displaying and interacting with the entities:

1. Goal Components:
   - `GoalList` - Displays a list of goals for an employee

2. Performance Review Components:
   - `PerformanceReviewList` - Displays a list of performance reviews for an employee or manager

3. Training Components:
   - `TrainingList` - Displays a list of training records for an employee

4. Topic Components:
   - `TopicList` - Displays a list of topics, either all topics or those created by a specific user

5. Project Components:
   - `ProjectList` - Displays a list of projects, optionally filtered by employee
   - `ProjectAssignmentList` - Displays a list of project assignments for a project or employee

## Usage Examples

### Managing Goals

```typescript
// Create a new goal
const goalId = await goalService.createGoal({
  employeeId: 123,
  title: 'Learn TypeScript',
  description: 'Complete TypeScript course and apply to project',
  targetDate: '2023-12-31',
  status: 'in-progress',
  progress: 25,
  category: 'development'
});

// Update goal progress
await goalService.updateGoal(goalId, {
  progress: 50,
  status: 'in-progress'
});

// Get all goals for an employee
const employeeGoals = await goalService.getEmployeeGoals(123);
```

### Managing Performance Reviews

```typescript
// Create a new performance review
const reviewId = await performanceReviewService.createPerformanceReview({
  employeeId: 123,
  managerId: 456,
  reviewDate: '2023-06-30',
  reviewPeriodStart: '2023-01-01',
  reviewPeriodEnd: '2023-06-30',
  status: 'draft'
});

// Add a goal to the review
await performanceReviewService.addGoalToReview(reviewId, goalId);

// Update the review status
await performanceReviewService.updatePerformanceReview(reviewId, {
  status: 'in-review'
});
```

### Using the API

```javascript
// Fetch all goals for an employee
fetch('/api/goals/employee/123')
  .then(response => response.json())
  .then(goals => console.log(goals))
  .catch(error => console.error('Error fetching goals:', error));

// Create a new performance review
fetch('/api/performance-reviews', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    employeeId: 123,
    managerId: 456,
    reviewDate: '2023-06-30',
    reviewPeriodStart: '2023-01-01',
    reviewPeriodEnd: '2023-06-30',
    status: 'draft'
  })
})
  .then(response => response.json())
  .then(data => console.log('Created review with ID:', data.id))
  .catch(error => console.error('Error creating review:', error));
```

### Using UI Components

```jsx
// Display goals for an employee
<GoalList employeeId={123} />

// Display performance reviews for a manager
<PerformanceReviewList managerId={456} />

// Display all projects
<ProjectList />

// Display project assignments for an employee
<ProjectAssignmentList employeeId={123} />
```

## Next Steps

1. Implement additional UI components for creating and editing entities
2. Add form validation for UI components
3. Implement authentication and authorization for API endpoints
4. Add comprehensive tests for the API endpoints
5. Implement reporting features that leverage the expanded schema 