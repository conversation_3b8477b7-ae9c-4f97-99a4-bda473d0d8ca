FROM node:lts

# Avoid warnings by switching to noninteractive
ENV DEBIAN_FRONTEND=noninteractive

# Configure apt and install packages
RUN apt-get update \
    && apt-get -y install --no-install-recommends apt-utils dialog 2>&1 \
    # Install git, process tools, lsb-release
    && apt-get -y install git iproute2 procps lsb-release \
    # Install additional development tools
    && apt-get -y install curl wget jq \
    # Clean up
    && apt-get autoremove -y \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/*

# Switch back to dialog for any ad-hoc use of apt-get
ENV DEBIAN_FRONTEND=dialog

# Set up non-root user
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Create the user's home if it doesn't exist
RUN mkdir -p /home/<USER>

# [Optional] Add sudo support
RUN apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

# Set the default user
USER $USERNAME

# Set up the workspace directory
WORKDIR /workspace

# Install global npm packages
RUN npm install -g npm@latest typescript ts-node nodemon

# Expose ports
# 3000: React frontend
# 3001: Express API
EXPOSE 3000 3001

# Set environment variables
ENV NODE_ENV=development
