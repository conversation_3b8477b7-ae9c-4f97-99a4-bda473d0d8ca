version: '3.8'

services:
  app:
    build: 
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/workspace:cached
    command: sleep infinity
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************/confidentcoaching
    depends_on:
      - db
    networks:
      - confidentcoaching-network

  db:
    image: postgres:14
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: confidentcoaching
    ports:
      - "5432:5432"
    networks:
      - confidentcoaching-network

networks:
  confidentcoaching-network:
    driver: bridge

volumes:
  postgres-data:
