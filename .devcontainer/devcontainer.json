{
  "name": "ConfidentCoaching Development",
  "dockerComposeFile": "docker-compose.yml",
  "service": "app",
  "workspaceFolder": "/workspace",
  
  // Set *default* container specific settings.json values on container create.
  "settings": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": true
    },
    "typescript.tsdk": "node_modules/typescript/lib",
    "typescript.enablePromptUseWorkspaceTsdk": true,
    "eslint.validate": [
      "javascript",
      "javascriptreact",
      "typescript",
      "typescriptreact"
    ]
  },
  
  // Add the IDs of extensions you want installed when the container is created.
  "extensions": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-azuretools.vscode-docker",
    "orta.vscode-jest",
    "firsttris.vscode-jest-runner",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "christian-kohler.npm-intellisense",
    "mikestead.dotenv",
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    "gruntfuggly.todo-tree",
    "visualstudioexptteam.vscodeintellicode"
  ],
  
  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [3000, 3001, 5432],
  
  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "npm install",
  
  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  "remoteUser": "node",
  
  // Features to add to the dev container. More info: https://containers.dev/features.
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "lts"
    },
    "ghcr.io/devcontainers/features/git:1": {
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "latest"
    }
  }
}
