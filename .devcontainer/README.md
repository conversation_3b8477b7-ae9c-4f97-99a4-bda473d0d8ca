# Development Container for ConfidentCoaching

This directory contains configuration files for setting up a consistent development environment using Visual Studio Code's Remote - Containers extension.

## What's Included

- **Node.js LTS**: For running the TypeScript/React/Express application
- **PostgreSQL 14**: For the database
- **Development Tools**: TypeScript, ts-node, nodemon, and other useful tools
- **VS Code Extensions**: ESLint, Prettier, TypeScript, Docker, Jest, and other productivity tools

## Prerequisites

1. [Docker](https://www.docker.com/products/docker-desktop) installed on your machine
2. [Visual Studio Code](https://code.visualstudio.com/) installed on your machine
3. [Remote - Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) installed in VS Code

## Getting Started

1. Clone the repository
2. Open the project folder in VS Code
3. When prompted, click "Reopen in Container" or run the "Remote-Containers: Reopen in Container" command from the Command Palette (F1)
4. VS Code will build the Docker container and set up the development environment (this may take a few minutes the first time)
5. Once the container is built, you can start developing!

## Container Structure

- **app service**: Node.js environment for running the application
- **db service**: PostgreSQL database for storing application data

## Ports

The following ports are forwarded to your local machine:

- **3000**: React frontend
- **3001**: Express API
- **5432**: PostgreSQL database

## Environment Variables

The following environment variables are set in the container:

- **NODE_ENV**: Set to `development`
- **DATABASE_URL**: Connection string for the PostgreSQL database

## Customizing the Container

You can customize the container by modifying the following files:

- **devcontainer.json**: VS Code settings and extensions
- **docker-compose.yml**: Service configuration
- **Dockerfile**: Node.js environment setup

## Troubleshooting

If you encounter any issues with the development container:

1. Try rebuilding the container using the "Remote-Containers: Rebuild Container" command
2. Check the Docker logs for any errors
3. Ensure that the required ports are not already in use on your machine
