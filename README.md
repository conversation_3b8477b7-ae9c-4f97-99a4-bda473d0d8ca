# ConfidentCoaching

A comprehensive application for managing employee performance reviews, training, projects, and professional development.

## Project Overview

ConfidentCoaching is a TypeScript-based application designed to help managers and employees track performance, set goals, manage projects, and coordinate training opportunities. The application provides a structured approach to employee development and performance management.

## Features

- **Performance Reviews**: Create, manage, and track employee performance reviews
- **Goal Setting**: Define and track professional development goals
- **Project Management**: Assign and monitor project participation
- **Training Coordination**: Schedule and track training opportunities
- **Topic Management**: Organize discussions and feedback by topic

## Tech Stack

- **Frontend**: React with TypeScript
- **Backend**: Express.js API
- **Database**: PostgreSQL
- **Validation**: Zod schema validation
- **Styling**: Bootstrap (or similar CSS framework)

## Development Environment

This project includes a Visual Studio Code devcontainer configuration for consistent development environments across all contributors. The devcontainer provides:

- Node.js LTS environment
- PostgreSQL database
- All necessary development tools and extensions

### Getting Started with the DevContainer

1. Ensure you have [Docker](https://www.docker.com/products/docker-desktop) and [VS Code](https://code.visualstudio.com/) with the [Remote - Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) installed
2. Clone this repository
3. Open the project in VS Code
4. When prompted, click "Reopen in Container" (or run the command from the Command Palette)
5. VS Code will build the container and set up the development environment
6. Once the container is ready, you can start developing!

For more details about the devcontainer setup, see the [.devcontainer/README.md](.devcontainer/README.md) file.

## Project Structure

- **src/api**: Express.js API routes and controllers
- **src/types**: TypeScript interfaces and type definitions
- **src/ui/components**: React components for the user interface
- **src/services**: Business logic and data access services
- **docs**: Project documentation

## Available Scripts

- **npm start**: Start the development server
- **npm run build**: Build the application for production
- **npm test**: Run the test suite
- **npm run lint**: Run ESLint to check code quality

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
