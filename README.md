# ConfidentCoaching

A comprehensive, production-ready application for managing employee performance reviews, training, projects, and professional development.

## Project Overview

ConfidentCoaching is a TypeScript-based application designed to help managers and employees track performance, set goals, manage projects, and coordinate training opportunities. The application provides a structured approach to employee development and performance management with enterprise-grade security, scalability, and reliability.

## Features

- **Performance Reviews**: Create, manage, and track employee performance reviews
- **Goal Setting**: Define and track professional development goals
- **Project Management**: Assign and monitor project participation
- **Training Coordination**: Schedule and track training opportunities
- **Topic Management**: Organize discussions and feedback by topic
- **User Authentication**: Secure JWT-based authentication with role-based access control
- **Real-time Monitoring**: Health checks, logging, and error tracking
- **Scalable Architecture**: Containerized deployment with load balancing support

## Tech Stack

- **Frontend**: React 18 with TypeScript, Vite build system
- **Backend**: Express.js API with TypeScript
- **Database**: PostgreSQL 14 with connection pooling
- **Authentication**: JWT tokens with bcrypt password hashing
- **Validation**: Zod schema validation
- **Testing**: Jest with React Testing Library
- **Containerization**: Docker with multi-stage builds
- **Reverse Proxy**: Nginx with SSL termination
- **CI/CD**: GitHub Actions with automated testing and deployment
- **Monitoring**: Winston logging, health checks, and error tracking

## Quick Start

### Development with DevContainer (Recommended)

1. Install [Docker](https://www.docker.com/products/docker-desktop) and [VS Code](https://code.visualstudio.com/) with the [Remote - Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
2. Clone this repository
3. Open the project in VS Code
4. When prompted, click "Reopen in Container"
5. Wait for the container to build and dependencies to install
6. Run `npm run dev` to start the development servers

### Local Development

1. **Prerequisites**: Node.js 18+, PostgreSQL 14+
2. **Install dependencies**: `npm install`
3. **Set up environment**: Copy `.env.example` to `.env` and configure
4. **Set up database**: `npm run migrate && npm run seed`
5. **Start development**: `npm run dev`

## Project Structure

```
src/
├── api/              # Express.js API routes and middleware
├── config/           # Configuration files (database, logging, etc.)
├── database/         # Database migrations and seeds
├── middleware/       # Custom middleware (auth, validation, etc.)
├── services/         # Business logic and data access services
├── types/            # TypeScript interfaces and type definitions
├── ui/               # React frontend application
├── utils/            # Utility functions and helpers
└── server.ts         # Main server entry point
```

## Available Scripts

### Development
- `npm run dev` - Start development servers (API + Frontend)
- `npm run dev:server` - Start API server only
- `npm run dev:client` - Start frontend only

### Building
- `npm run build` - Build for production
- `npm run build:server` - Build API server
- `npm run build:client` - Build frontend

### Testing
- `npm test` - Run test suite
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### Code Quality
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run typecheck` - Run TypeScript type checking

### Database
- `npm run migrate` - Run database migrations
- `npm run seed` - Seed database with initial data

### Docker
- `npm run docker:build` - Build Docker image
- `npm run docker:run` - Run Docker container

## Production Deployment

### Environment Setup

1. **Copy production environment template**:
   ```bash
   cp .env.production.example .env.production
   ```

2. **Configure production variables**:
   - Set secure `JWT_SECRET` (minimum 32 characters)
   - Configure database connection details
   - Set up SMTP for email notifications
   - Configure monitoring and logging

### Docker Deployment

1. **Build and deploy with Docker Compose**:
   ```bash
   # Set environment variables
   export POSTGRES_PASSWORD=your-secure-password
   export JWT_SECRET=your-jwt-secret
   export CORS_ORIGIN=https://your-domain.com

   # Deploy
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. **Run database migrations**:
   ```bash
   docker-compose -f docker-compose.prod.yml exec app npm run migrate
   docker-compose -f docker-compose.prod.yml exec app npm run seed
   ```

### Manual Deployment

1. **Build the application**:
   ```bash
   npm ci --only=production
   npm run build
   ```

2. **Set up database**:
   ```bash
   npm run migrate
   npm run seed
   ```

3. **Start the application**:
   ```bash
   npm start
   ```

### SSL Configuration

For production, configure SSL certificates in the `nginx.conf` file:

1. Place SSL certificates in the `ssl/` directory
2. Uncomment and configure the HTTPS server block in `nginx.conf`
3. Update the certificate paths and domain name

### Monitoring and Maintenance

- **Health Check**: `GET /health` endpoint for monitoring
- **Logs**: Application logs are stored in `logs/` directory
- **Database Backups**: Configure regular PostgreSQL backups
- **Updates**: Use the CI/CD pipeline for automated deployments

## Security Features

- **Authentication**: JWT-based authentication with secure password hashing
- **Authorization**: Role-based access control (admin, manager, employee)
- **Input Validation**: Comprehensive request validation with Zod schemas
- **Security Headers**: Helmet.js for security headers
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS**: Configurable CORS policies
- **SQL Injection Protection**: Parameterized queries with pg library
- **XSS Protection**: Content Security Policy headers

## Testing

The application includes comprehensive testing:

- **Unit Tests**: Individual function and component testing
- **Integration Tests**: API endpoint testing
- **Security Tests**: Authentication and authorization testing
- **Performance Tests**: Load testing capabilities

Run tests with:
```bash
npm test                # Run all tests
npm run test:coverage   # Run with coverage report
npm run test:watch      # Run in watch mode
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token

### Core Endpoints
- `GET /api/goals` - List goals
- `POST /api/goals` - Create goal
- `GET /api/performance-reviews` - List performance reviews
- `POST /api/performance-reviews` - Create performance review
- `GET /api/trainings` - List training records
- `POST /api/trainings` - Create training record
- `GET /api/projects` - List projects
- `POST /api/projects` - Create project
- `GET /api/topics` - List topics
- `POST /api/topics` - Create topic

All API endpoints require authentication except `/health` and `/api/auth/login`.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes following the coding standards
4. Add tests for new functionality
5. Ensure all tests pass (`npm test`)
6. Commit your changes (`git commit -m 'Add some amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Ensure code passes linting (`npm run lint`)
- Update documentation as needed

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Verify PostgreSQL is running
   - Check connection string in `.env`
   - Ensure database exists and user has permissions

2. **Build Failures**:
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check Node.js version (requires 18+)
   - Verify TypeScript compilation: `npm run typecheck`

3. **Docker Issues**:
   - Rebuild containers: `docker-compose down && docker-compose up --build`
   - Check Docker logs: `docker-compose logs`
   - Verify port availability

### Getting Help

- Check the [Issues](https://github.com/your-org/confident-coaching/issues) page
- Review the [Documentation](./docs/) directory
- Contact the development team

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with modern TypeScript and React
- Inspired by best practices in enterprise application development
- Security-first approach with comprehensive testing
