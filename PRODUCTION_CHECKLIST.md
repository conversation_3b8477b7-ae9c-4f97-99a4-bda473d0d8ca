# Production Deployment Checklist

This checklist ensures that ConfidentCoaching is properly configured and secured for production deployment.

## Pre-Deployment Checklist

### Environment Configuration
- [ ] Copy `.env.production.example` to `.env.production`
- [ ] Set secure `JWT_SECRET` (minimum 32 characters, use a password generator)
- [ ] Configure production database connection (`DATABASE_URL`)
- [ ] Set correct `CORS_ORIGIN` to your domain
- [ ] Configure SMTP settings for email notifications
- [ ] Set appropriate log levels (`LOG_LEVEL=info` or `warn`)
- [ ] Configure monitoring (Sentry DSN if using)

### Security Configuration
- [ ] Change default database passwords
- [ ] Use strong passwords for all accounts (minimum 12 characters)
- [ ] Enable SSL/TLS for database connections (`DB_SSL=true`)
- [ ] Configure rate limiting appropriately for your traffic
- [ ] Review and adjust CORS settings
- [ ] Set up SSL certificates for HTTPS

### Infrastructure Setup
- [ ] Provision production server with adequate resources
  - [ ] Minimum 2 CPU cores
  - [ ] Minimum 4GB RAM
  - [ ] Minimum 20GB storage
- [ ] Install Docker and Docker Compose
- [ ] Configure firewall rules
  - [ ] Allow HTTP (80) and HTTPS (443)
  - [ ] Allow SSH (22) for management
  - [ ] Block direct database access (5432) from external networks
- [ ] Set up backup storage location
- [ ] Configure log rotation

### Database Setup
- [ ] Create production database
- [ ] Create database user with appropriate permissions
- [ ] Configure connection pooling settings
- [ ] Set up automated backups
- [ ] Test database connectivity

## Deployment Process

### Initial Deployment
- [ ] Clone repository to production server
- [ ] Copy and configure environment files
- [ ] Run deployment script: `./scripts/deploy.sh`
- [ ] Verify all containers are running: `docker-compose -f docker-compose.prod.yml ps`
- [ ] Run database migrations: `docker-compose -f docker-compose.prod.yml exec app npm run migrate`
- [ ] Seed initial data: `docker-compose -f docker-compose.prod.yml exec app npm run seed`
- [ ] Test health endpoint: `curl http://localhost:3001/health`

### SSL/HTTPS Configuration
- [ ] Obtain SSL certificates (Let's Encrypt recommended)
- [ ] Place certificates in `ssl/` directory
- [ ] Update `nginx.conf` with certificate paths
- [ ] Uncomment HTTPS server block in `nginx.conf`
- [ ] Update domain name in nginx configuration
- [ ] Restart nginx container
- [ ] Test HTTPS connectivity
- [ ] Configure HTTP to HTTPS redirect

### DNS Configuration
- [ ] Point domain A record to server IP
- [ ] Configure www subdomain (if needed)
- [ ] Set up CDN (optional but recommended)
- [ ] Configure DNS TTL appropriately

## Post-Deployment Verification

### Functional Testing
- [ ] Test user registration/login
- [ ] Test goal creation and management
- [ ] Test performance review workflow
- [ ] Test training record management
- [ ] Test project assignment functionality
- [ ] Test topic creation and discussion
- [ ] Verify email notifications work
- [ ] Test file upload functionality (if implemented)

### Security Testing
- [ ] Verify HTTPS is working and HTTP redirects
- [ ] Test authentication with invalid credentials
- [ ] Verify JWT token expiration
- [ ] Test rate limiting by making rapid requests
- [ ] Check security headers with online tools
- [ ] Verify CORS policy is working correctly
- [ ] Test SQL injection protection
- [ ] Verify XSS protection

### Performance Testing
- [ ] Test application response times
- [ ] Verify database query performance
- [ ] Test with multiple concurrent users
- [ ] Monitor memory and CPU usage
- [ ] Check log file sizes and rotation

### Monitoring Setup
- [ ] Configure application monitoring
- [ ] Set up database monitoring
- [ ] Configure server resource monitoring
- [ ] Set up alerting for critical issues
- [ ] Test backup and restore procedures
- [ ] Configure log aggregation (if using external service)

## Ongoing Maintenance

### Daily Tasks
- [ ] Check application health endpoint
- [ ] Review error logs
- [ ] Monitor server resources
- [ ] Verify backups completed successfully

### Weekly Tasks
- [ ] Review security logs
- [ ] Check for application updates
- [ ] Monitor database performance
- [ ] Review user activity logs
- [ ] Test backup restoration process

### Monthly Tasks
- [ ] Update dependencies (after testing)
- [ ] Review and rotate logs
- [ ] Security audit and vulnerability scan
- [ ] Performance optimization review
- [ ] Backup retention cleanup

## Emergency Procedures

### Application Down
1. Check container status: `docker-compose -f docker-compose.prod.yml ps`
2. Check logs: `docker-compose -f docker-compose.prod.yml logs`
3. Restart if needed: `./scripts/deploy.sh restart`
4. If database issues, restore from backup

### Database Issues
1. Check database container: `docker-compose -f docker-compose.prod.yml logs db`
2. Check disk space: `df -h`
3. If corruption, restore from latest backup
4. Contact database administrator if needed

### Security Incident
1. Immediately change all passwords
2. Revoke all active JWT tokens (restart application)
3. Review access logs
4. Update security configurations
5. Notify users if data was compromised

## Rollback Procedure

If deployment fails or issues are discovered:

1. **Immediate Rollback**:
   ```bash
   # Stop current deployment
   docker-compose -f docker-compose.prod.yml down
   
   # Restore database from backup
   docker-compose -f docker-compose.prod.yml up -d db
   cat backups/backup_YYYYMMDD_HHMMSS.sql | docker-compose -f docker-compose.prod.yml exec -T db psql -U postgres confidentcoaching
   
   # Deploy previous version
   git checkout previous-stable-tag
   ./scripts/deploy.sh
   ```

2. **Verify rollback success**:
   - Test critical functionality
   - Check health endpoint
   - Verify user access

## Support Contacts

- **Development Team**: [<EMAIL>]
- **Infrastructure Team**: [<EMAIL>]
- **Security Team**: [<EMAIL>]
- **On-call Emergency**: [emergency-contact]

## Documentation Links

- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Security Guidelines](./docs/security.md)
- [Troubleshooting Guide](./docs/troubleshooting.md)

---

**Note**: This checklist should be reviewed and updated regularly as the application evolves and new security requirements emerge.
