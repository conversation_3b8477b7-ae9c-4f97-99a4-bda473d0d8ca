# Production Environment Configuration
NODE_ENV=production
PORT=3001
CLIENT_PORT=3000

# Database Configuration
DATABASE_URL=**************************************/confidentcoaching
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=confidentcoaching
DB_USER=your-db-user
DB_PASSWORD=your-secure-db-password
DB_SSL=true
DB_POOL_MIN=5
DB_POOL_MAX=20

# Authentication - CHANGE THESE IN PRODUCTION!
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Security
CORS_ORIGIN=https://your-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Email Configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
FROM_EMAIL=<EMAIL>

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Monitoring
SENTRY_DSN=your-sentry-dsn-url
HEALTH_CHECK_INTERVAL=30000

# Docker Compose Variables
POSTGRES_PASSWORD=your-secure-postgres-password
DOCKER_USERNAME=your-docker-username
